version: '3'

vars:
  GOOS: "{{default OS .GOOS}}"
  MVNW: '{{if eq .GOOS "windows"}}mvnw.cmd{{else}}./mvnw{{end}}'
  DC_DIR: "deployment/docker"
  INFRA_DC_FILE: "{{.DC_DIR}}/infra.yml"
  APPS_DC_FILE: "{{.DC_DIR}}/apps.yml"
  MONITORING_DC_FILE: "{{.DC_DIR}}/monitoring.yml"
  SLEEP_CMD: '{{if eq .GOOS "windows"}}timeout{{else}}sleep{{end}}'

tasks:
  default:
    cmds:
      - task: test
  test:
    deps: [ format ]
    cmds:
      - "{{.MVNW}} clean verify"

  format:
    cmds:
      - "{{.MVNW}} spotless:apply"

  build:
    cmds:
      - "{{.MVNW}} -pl catalog-service spring-boot:build-image -DskipTests"
      - "{{.MVNW}} -pl order-service spring-boot:build-image -DskipTests"
      - "{{.MVNW}} -pl notification-service spring-boot:build-image -DskipTests"
      - "{{.MVNW}} -pl api-gateway spring-boot:build-image -DskipTests"
      - "{{.MVNW}} -pl bookstore-webapp spring-boot:build-image -DskipTests"

  start_infra:
    cmds:
      - "docker compose -f {{.INFRA_DC_FILE}} up -d"

  stop_infra:
    cmds:
      - "docker compose -f {{.INFRA_DC_FILE}} stop"
      - "docker compose -f {{.INFRA_DC_FILE}} rm -f"

  restart_infra:
    cmds:
      - task: stop_infra
      - task: sleep
      - task: start_infra

  start_monitoring:
    cmds:
      - "docker compose -f {{.MONITORING_DC_FILE}} up -d"

  stop_monitoring:
    cmds:
      - "docker compose -f {{.MONITORING_DC_FILE}} stop"
      - "docker compose -f {{.MONITORING_DC_FILE}} rm -f"

  restart_monitoring:
    cmds:
      - task: stop_monitoring
      - task: sleep
      - task: start_monitoring

  start_apps:
    cmds:
      - "docker compose -f {{.APPS_DC_FILE}} up -d"

  stop_apps:
    cmds:
      - "docker compose -f {{.APPS_DC_FILE}} stop"
      - "docker compose -f {{.APPS_DC_FILE}} rm -f"

  restart_apps:
    cmds:
      - task: stop_apps
      - task: sleep
      - task: start_apps

  start:
    deps: [ build ]
    cmds:
      - "docker compose -f {{.INFRA_DC_FILE}} -f {{.APPS_DC_FILE}} up -d"

  stop:
    cmds:
      - "docker compose -f {{.INFRA_DC_FILE}} -f {{.APPS_DC_FILE}} stop"
      - "docker compose -f {{.INFRA_DC_FILE}} -f {{.APPS_DC_FILE}} rm -f"

  restart:
    cmds:
      - task: stop
      - task: sleep
      - task: start

  sleep:
    vars:
      DURATION: "{{default 5 .DURATION}}"
    cmds:
      - "{{.SLEEP_CMD}} {{.DURATION}}"
