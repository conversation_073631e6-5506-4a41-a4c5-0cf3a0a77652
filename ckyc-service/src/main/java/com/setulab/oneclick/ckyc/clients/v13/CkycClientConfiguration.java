package com.setulab.oneclick.ckyc.clients.v13;

import com.setulab.oneclick.ckyc.ApplicationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
public class CkycClientConfiguration {

    /**
     * RestTemplate configuration
     */
    @Bean
    public RestTemplate ckycRestTemplate(RestTemplateBuilder templateBuilder, ApplicationProperties properties) {
        return templateBuilder
                .rootUri(properties.baseUrl())
                .requestFactory(this::createRequestFactory)
                .build();
    }

    private SimpleClientHttpRequestFactory createRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(Duration.ofSeconds(30));
        factory.setReadTimeout(Duration.ofSeconds(60));
        return factory;
    }
}