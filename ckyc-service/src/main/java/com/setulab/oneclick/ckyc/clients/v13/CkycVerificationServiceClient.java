package com.setulab.oneclick.ckyc.clients.v13;

/**
 * Client interface for CKYC Verification Service API v1.3
 * Based on CKYC Secured Search and Download API documentation
 */
public interface CkycVerificationServiceClient {

    /**
     * CKYC Search API - Search for CKYC details using ID number and type
     *
     * @param requestId Unique request identifier
     * @param payload   Encrypted request payload
     * @return Mono containing the CKYC search response
     */
    String verifyCkyc(String requestId, String payload);

    /**
     * CKYC Download API - Download CKYC details using CKYC number and
     * authentication
     *
     * @param requestId Unique request identifier
     * @param payload   Encrypted request payload
     * @return Mono containing the CKYC download response
     */
    String downloadCkyc(String requestId, String payload);

    /**
     * CKYC OTP Validation API - Validate OTP for download request
     *
     * @param requestId Unique request identifier
     * @param otp       OTP value to validate
     * @return Mono containing the OTP validation response
     */
    String validateOtp(String requestId, String otp);
}