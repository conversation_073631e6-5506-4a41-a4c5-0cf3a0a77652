package com.setulab.oneclick.ckyc.clients.v13;

import com.setulab.oneclick.ckyc.ApplicationProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate-based implementation of CKYC Verification Service Client
 * Provides blocking HTTP client functionality
 */
@Slf4j
@Service("ckycRestTemplateClient")
@RequiredArgsConstructor
public class CkycVerificationServiceClientImpl implements CkycVerificationServiceClient {

    private final RestTemplate restTemplate;
    private final ApplicationProperties applicationProperties;


    @Override
    public String verifyCkyc(String requestId, String payload) {

        String url = applicationProperties.baseUrl() + "/Search/ckycverificationservice/verify";
        log.info("Initiating CKYC verify (RestTemplate) for requestId: {}, URl: {}", requestId, url);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(payload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("CKYC verify response: {}", responseString);

        return responseString;
    }

    @Override
    public String downloadCkyc(String requestId, String payload) {
        String url = applicationProperties.baseUrl() + "/Search/ckycverificationservice/download";
        log.info("Initiating CKYC download (RestTemplate) for requestId: {}, URl: {}", requestId, url);
        return "";
    }

    @Override
    public String validateOtp(String requestId, String otp) {
        String url = applicationProperties.baseUrl() + "/Search/ckycverificationservice/ValidateOTP";
        log.info("Initiating CKYC ValidateOTP (RestTemplate) for requestId: {}, URl: {}", requestId, url);
        return "";
    }
}