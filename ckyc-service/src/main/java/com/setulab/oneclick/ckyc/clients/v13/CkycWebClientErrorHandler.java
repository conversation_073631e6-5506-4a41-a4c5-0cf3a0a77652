package com.setulab.oneclick.ckyc.clients.v13;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

/**
 * Error handler for CKYC WebClient
 */
@Slf4j
@Component
public class CkycWebClientErrorHandler {

    /**
     * Handle HTTP errors from CKYC API
     */
    public Mono<ClientResponse> handleError(ClientResponse response) {
        int statusCode = response.statusCode().value();

        log.error("CKYC API error - Status: {}, Headers: {}", statusCode, response.headers());

        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("CKYC API error body: {}", errorBody);

                    switch (statusCode) {
                        case 401:
                            return Mono.error(new WebClientResponseException(
                                    "CKYC API authentication failed",
                                    statusCode,
                                    "Unauthorized",
                                    response.headers().asHttpHeaders(),
                                    errorBody.getBytes(),
                                    null));
                        case 403:
                            return Mono.error(new WebClientResponseException(
                                    "CKYC API access forbidden",
                                    statusCode,
                                    "Forbidden",
                                    response.headers().asHttpHeaders(),
                                    errorBody.getBytes(),
                                    null));
                        case 400:
                            return Mono.error(new WebClientResponseException(
                                    "CKYC API bad request - check request format",
                                    statusCode,
                                    "Bad Request",
                                    response.headers().asHttpHeaders(),
                                    errorBody.getBytes(),
                                    null));
                        case 500:
                            return Mono.error(new WebClientResponseException(
                                    "CKYC API internal server error",
                                    statusCode,
                                    "Internal Server Error",
                                    response.headers().asHttpHeaders(),
                                    errorBody.getBytes(),
                                    null));
                        default:
                            return Mono.error(new WebClientResponseException(
                                    "CKYC API error: " + statusCode,
                                    statusCode,
                                    "Error",
                                    response.headers().asHttpHeaders(),
                                    errorBody.getBytes(),
                                    null));
                    }
                });
    }
}