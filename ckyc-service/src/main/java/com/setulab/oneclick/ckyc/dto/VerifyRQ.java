package com.setulab.oneclick.ckyc.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VerifyRQ {

    @NotEmpty
    @JsonProperty(value = "idType")
    private String idType;

    @NotEmpty
    @JsonProperty(value = "idNumber")
    private String idNumber;

    @JsonProperty(value = "requestId")
    private String requestId;
}
