package com.setulab.oneclick.ckyc.service;

import com.setulab.oneclick.ckyc.clients.v13.CkycVerificationServiceClientImpl;
import com.setulab.oneclick.ckyc.dto.VerifyRQ;
import com.setulab.oneclick.ckyc.enums.RandomNumberGenerationMethod;
import com.setulab.oneclick.ckyc.utils.CkycSignUtil;
import com.setulab.oneclick.ckyc.utils.RandomNumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableEntryException;
import java.security.cert.CertificateException;

@Slf4j
@Service
public class CkycVerificationServiceImpl {

    @Autowired
    CkycVerificationServiceClientImpl ckycVerificationServiceClientImpl;
    CkycSignUtil ckycSignUtil;

    //LIVE
    private static final String FI_CERTIFICATE_KEY_STORE_PATH = "resources" + File.separator + "ckyc_rohit.pfx";
    private static final String CERSAI_PUBLIC_KEY_PATH = "resources" + File.separator + "ckyc_CersaiSignPublicKey.cer";
    private static final String FI_CERTIFICATE_KEY_STORE_PASSWORD = "roh123";

    public CkycVerificationServiceImpl() throws UnrecoverableEntryException, CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException {
        // Using Dongle PKCS11
        // this.ckycSignUtilV1 = new CkycSignUtilV1("1.3", "PKCS11", "IN0888", CERSAI_PUBLIC_KEY_PATH, FI_PKCS11_CONFIG_FILE_PATH, FI_PKCS11_KEY_STORE_PASSWORD);
        // Using File PKCS12
        this.ckycSignUtil = new CkycSignUtil("1.3", "PKCS12", "IN0888", CERSAI_PUBLIC_KEY_PATH, FI_CERTIFICATE_KEY_STORE_PATH, FI_CERTIFICATE_KEY_STORE_PASSWORD);
    }

    public String ckycVerify(VerifyRQ verifyRQ) {
        try {

            String requestId = verifyRQ.getRequestId();

            log.info("Received CKYC verification request: {}", verifyRQ);

            // Check if verification request has request id
            // If it does, log the request id
            // If it doesn't, generate a new request id
            if (verifyRQ.getRequestId() == null || verifyRQ.getRequestId().isEmpty()) {
                requestId = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
                log.info("Request id not provided, generated new request id: {}", requestId);
            }

            // Request payload
            String payload = ckycSignUtil.getCkycVerifySignedRequest(verifyRQ.getIdType(), verifyRQ.getIdNumber(), requestId);
            log.info("CKYC verification request payload: {}", payload);

            //Send Request
            String responseString = ckycVerificationServiceClientImpl.verifyCkyc(requestId, payload);

            //Decrypt Response
            String decryptedResponse = ckycSignUtil.getCkycVerifyDecryptedResponse(responseString);
            log.info("CKYC verification successful: {}", decryptedResponse);

            return decryptedResponse;
        } catch (Exception e) {
            log.error("CKYC verification failed: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public String ckycDownload() {
        return "";
    }

    public String ckycValidateOtp() {
        return "";
    }
}
