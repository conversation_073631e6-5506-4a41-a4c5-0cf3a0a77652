package com.setulab.oneclick.ckyc.utils;


import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.pkcs.RSAPrivateKey;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.digests.SHA1Digest;
import org.bouncycastle.crypto.encodings.OAEPEncoding;
import org.bouncycastle.crypto.engines.AESEngine;
import org.bouncycastle.crypto.engines.RSAEngine;
import org.bouncycastle.crypto.paddings.PKCS7Padding;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.RSAKeyParameters;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.web.util.HtmlUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.crypto.*;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;
import javax.xml.crypto.XMLStructure;
import javax.xml.crypto.dom.DOMStructure;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.SignedInfo;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMSignContext;
import javax.xml.crypto.dsig.dom.DOMValidateContext;
import javax.xml.crypto.dsig.keyinfo.KeyInfo;
import javax.xml.crypto.dsig.keyinfo.KeyInfoFactory;
import javax.xml.crypto.dsig.keyinfo.X509Data;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.MGF1ParameterSpec;
import java.security.spec.RSAPrivateKeySpec;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
public class CkycSignUtil {

    static {
        // Register Bouncy Castle as a provider for PKCS12
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String API_VERSION = "1.3";
    private static final String KEY_STORE_TYPE = "PKCS12";
    private static final String FI_CODE = "IN0888";
    private static final String CERSAI_PUBLIC_KEY_PATH = "ckyc_CersaiSignPublicKey.cer";
    private static final String FI_CERTIFICATE_KEY_STORE_PATH = "ckyc_rohit.pfx";
    private static final String FI_CERTIFICATE_KEY_STORE_PASSWORD = "roh123";
    private static final String FI_CERTIFICATE_KEY_ALIAS = "te-9d800711-1c56-4a7e-8c77-d3c22ffe5da0";

    private static final String KEY_STORE_TYPE_DONGLE = "PKCS11";

    private static final List<String> keyStoreTypes = new ArrayList<>(Arrays.asList(KEY_STORE_TYPE, KEY_STORE_TYPE_DONGLE));


    private List<String> apiVersions = new ArrayList<>(Arrays.asList("1.2", "1.3"));
    private String apiVersion = "1.3";
    private String fiCode = "IN0888";

    private PublicKey cersaiPublicKey;
    private KeyStore.PrivateKeyEntry privateKeyEntry;

    private Provider currentProvider = Security.getProvider(BouncyCastleProvider.PROVIDER_NAME);

    public CkycSignUtil(String apiVersion, String keyStoreType, String fiCode, String cersaiPublicKeyFilePath, String fiKeyStoreOrConfigFilePath, String fiKeyStorePassword, String fiPrivateKeyAlias) throws CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException, UnrecoverableEntryException {
        if (!apiVersions.contains(apiVersion)) {
            log.info("Invalid api version: {}", apiVersion);
            throw new RuntimeException("Invalid api version: " + apiVersion);
        }

        //Setting api version
        this.apiVersion = apiVersion;

        //Setting fi code
        this.fiCode = fiCode;

        if (!keyStoreTypes.contains(keyStoreType)) {
            throw new RuntimeException("Invalid key store type: " + keyStoreType);
        }

        //Reading and loading cersai public key
        this.cersaiPublicKey = getCersaiPublicKey(cersaiPublicKeyFilePath);
        if (this.cersaiPublicKey == null) {
            throw new RuntimeException("Cersai public key could not be read. Please check value of cersai public key path, and restart the Auth Client");
        }

        if (keyStoreType.equals(KEY_STORE_TYPE)) {
            //Reading and loading fi private key pfx
            this.privateKeyEntry = getPrivateKeyFromKeyStore(fiKeyStoreOrConfigFilePath, fiKeyStorePassword, fiPrivateKeyAlias);
            if (this.privateKeyEntry == null) {
                throw new RuntimeException("PKCS12 : Key could not be read for digital signature. Please check value of signature alias and signature password, and restart the Auth Client");
            }
        }

        if (keyStoreType.equals(KEY_STORE_TYPE_DONGLE)) {
            //Reading and loading fi private key from dongle
            this.privateKeyEntry = getPrivateKeyFromDongle(fiKeyStoreOrConfigFilePath, fiKeyStorePassword, fiPrivateKeyAlias);
            if (this.privateKeyEntry == null) {
                throw new RuntimeException("PKCS11 : Key could not be read for digital signature. Please check value of signature alias and signature password, and restart the Auth Client");
            }
        }
    }

    public CkycSignUtil(String apiVersion, String keyStoreType, String fiCode, String cersaiPublicKeyFilePath, String fiKeyStoreOrConfigFilePath, String fiKeyStorePassword) throws CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException, UnrecoverableEntryException {
        if (!apiVersions.contains(apiVersion)) {
            log.info("Invalid api version: {}", apiVersion);
            throw new RuntimeException("Invalid api version: " + apiVersion);
        }

        //Setting api version
        this.apiVersion = apiVersion;
        //Setting fi code
        this.fiCode = fiCode;

        if (!keyStoreTypes.contains(keyStoreType)) {
            throw new RuntimeException("Invalid key store type: " + keyStoreType);
        }

        //Reading and loading cersai public key
        this.cersaiPublicKey = getCersaiPublicKey(cersaiPublicKeyFilePath);
        if (this.cersaiPublicKey == null) {
            throw new RuntimeException("Cersai public key could not be read. Please check value of cersai public key path, and restart the Auth Client");
        }

        if (keyStoreType.equals(KEY_STORE_TYPE)) {
            //Reading and loading fi private key pfx
            this.privateKeyEntry = getPrivateKeyFromKeyStore(fiKeyStoreOrConfigFilePath, fiKeyStorePassword, null);
            if (this.privateKeyEntry == null) {
                throw new RuntimeException("PKCS12 : Key could not be read for digital signature. Please check value of signature alias and signature password, and restart the Auth Client");
            }
        }

        if (keyStoreType.equals(KEY_STORE_TYPE_DONGLE)) {
            //Reading and loading fi private key from dongle
            this.privateKeyEntry = getPrivateKeyFromDongle(fiKeyStoreOrConfigFilePath, fiKeyStorePassword, null);
            if (this.privateKeyEntry == null) {
                throw new RuntimeException("PKCS11 : Key could not be read for digital signature. Please check value of signature alias and signature password, and restart the Auth Client");
            }
        }
    }

    private String generateTimestamp() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
        return now.format(formatter);
    }

    private byte[] getInitialXml(String idType, String idNumber, String timestamp) {
        if (timestamp == null) {
            timestamp = generateTimestamp();
        }
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?><PID_DATA><DATE_TIME>%s</DATE_TIME><ID_NO>%s</ID_NO><ID_TYPE>%s</ID_TYPE></PID_DATA>", timestamp, idNumber, idType).getBytes(StandardCharsets.UTF_8);
    }

    //============================== Ckyc Verify ==============================//
    public String getCkycVerifySignedRequest(String idType, String idNumber, String requestId) throws GeneralSecurityException, InvalidCipherTextException, IOException {

        //Step 1: Get Initial PID XML With Timestamp
        byte[] initialXml = this.getInitialXmlForCkycVerify(idType, idNumber);
        //Step 2: Generate Session Key
        byte[] sessionKey = this.generateSessionKey();
        //Step 3: Encrypt Initial XML With Timestamp
        byte[] encryptedInitialXml = this.encryptUsingSessionKey(sessionKey, initialXml);
        //Step 4: Encoding Encrypted Initial XML With Base64
        String encodedEncryptedInitialXml = Base64.getEncoder().encodeToString(encryptedInitialXml);
        //Step 5: Encrypt Session Key Using CERSAI Public Key
        byte[] encryptedSessionKey = this.encryptUsingPublicKey(sessionKey);
        //Step 6: Encoding Encrypted Session Key With Base64
        String encodedEncryptedSessionKey = Base64.getEncoder().encodeToString(encryptedSessionKey);
        //Step 7: Generate Final XML With Encoded Encrypted Initial PID XML and Encoded Encrypted Session Key
        String finalXml = this.getCkycVerifyFinalXml(this.fiCode, requestId, encodedEncryptedInitialXml, encodedEncryptedSessionKey);
        //Step 8: Sign Final XML With FI Private Key
        String signedFinalXml = signXML(finalXml, false);
        return signedFinalXml;
    }

    public String getCkycVerifyDecryptedResponse(String responseXml) throws Exception {

        //Step 1: Verify Digital Signature
        boolean isValidSignature = this.verifySignatureWithPublicKey(responseXml);

        //Step 2: Minify and encode XML response string
        String minifiedAndEncodedXmlString = this.minifyAndEncodeXml(responseXml);

        //Step 3: Converting XML response string to XML document
        Document XMLDoc = this.convertXmlStringToDocument(minifiedAndEncodedXmlString);

        //Step 4: Decrypt Session Key Using FI Private Key
        byte[] sessionKey = this.getSessionKey(XMLDoc);

        //Step 5: Decrypt Initial PID XML With Session Key
        byte[] PIDData = this.getPIDData(XMLDoc, sessionKey);

        return this.convertBytesToString(PIDData);
    }

    private String getCkycVerifyFinalXml(String fiCode, String requestId, String encodedEncryptedInitialXml, String encodedEncryptedSessionKey) {
        // Implement XML creation logic
        // You may want to use a proper XML builder or template engine for this
        //LIVE=> IN0888
        //UAT=> IN1488
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?><REQ_ROOT>" + "<HEADER><FI_CODE>%s</FI_CODE><REQUEST_ID>%s</REQUEST_ID><VERSION>1.3</VERSION> </HEADER>" + "<CKYC_INQ><PID>%s</PID><SESSION_KEY>%s</SESSION_KEY></CKYC_INQ></REQ_ROOT>", fiCode, requestId, encodedEncryptedInitialXml, encodedEncryptedSessionKey);
    }


    private byte[] getInitialXmlForCkycVerify(String idType, String idNumber) {
        String timestamp = generateTimestamp();
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?><PID_DATA><DATE_TIME>%s</DATE_TIME><ID_NO>%s</ID_NO><ID_TYPE>%s</ID_TYPE></PID_DATA>", timestamp, idNumber, idType).getBytes(StandardCharsets.UTF_8);
    }

    //============================== Ckyc Download ==============================//
    public String getCkycDownloadSignedRequest(String requestId, String ckycNumberOrReference, String authFactorType, String authFactor) throws GeneralSecurityException, InvalidCipherTextException, IOException {

        //Step 1: Get Initial PID XML With Timestamp
        byte[] initialXml = this.getInitialXmlForCkycDownload(ckycNumberOrReference, authFactorType, authFactor);
        log.info("Ckyc Download Initial XML:{}", this.convertBytesToString(initialXml));
        //Step 2: Generate Session Key
        byte[] sessionKey = this.generateSessionKey();
        log.info("Ckyc Download Session Key:{}", this.convertBytesToString(sessionKey));
        //Step 3: Encrypt Initial XML With Timestamp
        byte[] encryptedInitialXml = this.encryptUsingSessionKey(sessionKey, initialXml);
        log.info("Ckyc Download Encrypted Initial XML:{}", encryptedInitialXml);
        //Step 4: Encoding Encrypted Initial XML With Base64
        String encodedEncryptedInitialXml = Base64.getEncoder().encodeToString(encryptedInitialXml);
        log.info("Ckyc Download Encoded Encrypted Initial XML:{}", encodedEncryptedInitialXml);
        //Step 5: Encrypt Session Key Using CERSAI Public Key
        byte[] encryptedSessionKey = this.encryptUsingPublicKey(sessionKey);
        //Step 6: Encoding Encrypted Session Key With Base64
        String encodedEncryptedSessionKey = Base64.getEncoder().encodeToString(encryptedSessionKey);
        log.info("Ckyc Download Encoded Encrypted Session Key:{}", encodedEncryptedSessionKey);
        //Step 7: Generate Final XML With Encoded Encrypted Initial PID XML and Encoded Encrypted Session Key
        String finalXml = this.getCkycDownloadFinalXml(this.fiCode, requestId, encodedEncryptedInitialXml, encodedEncryptedSessionKey);
        log.info("Ckyc Download Final XML:{}", finalXml);
        //Step 8: Sign Final XML With FI Private Key
        String signedFinalXml = signXML(finalXml, false);
        return signedFinalXml;
    }

    public String getCkycDownloadDecryptedResponse() {

        return "OK";
    }

    public String getCkycDownloadFinalXml(String fiCode, String requestId, String encodedEncryptedInitialXml, String encodedEncryptedSessionKey) {

        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + "<CKYC_DOWNLOAD_REQUEST>" + "<HEADER><FI_CODE>%s</FI_CODE><REQUEST_ID>%s</REQUEST_ID><VERSION>1.3</VERSION></HEADER>" + "<CKYC_INQ><SESSION_KEY>%s</SESSION_KEY><PID>%s</PID></CKYC_INQ>" + "</CKYC_DOWNLOAD_REQUEST>", fiCode, requestId, encodedEncryptedSessionKey, encodedEncryptedInitialXml);
    }

    private byte[] getInitialXmlForCkycDownload(String ckycNumberOrCkycReferenceNumber, String authFactorType, String authFactor) {
        String timestamp = generateTimestamp();
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" + "<PID_DATA><DATE_TIME>%s</DATE_TIME><CKYC_NO>%s</CKYC_NO><AUTH_FACTOR_TYPE>%s</AUTH_FACTOR_TYPE><AUTH_FACTOR>%s</AUTH_FACTOR></PID_DATA>", timestamp, ckycNumberOrCkycReferenceNumber, authFactorType, authFactor).getBytes(StandardCharsets.UTF_8);
    }


    //============================== Ckyc Validate Otp ==============================//
    public String getCkycValidateOtpSignedRequest(String requestId, String otp) throws GeneralSecurityException, InvalidCipherTextException, IOException {
        //Step 1: Get Initial PID XML With Timestamp
        byte[] initialXml = this.getInitialXmlForCkycValidateOtp(otp, "Y");
        log.info("Ckyc Validate Otp Initial XML:{}", this.convertBytesToString(initialXml));
        //Step 2: Generate Session Key
        byte[] sessionKey = this.generateSessionKey();
        log.info("Ckyc Validate Otp Session Key:{}", this.convertBytesToString(sessionKey));
        //Step 3: Encrypt Initial XML With Timestamp
        byte[] encryptedInitialXml = this.encryptUsingSessionKey(sessionKey, initialXml);
        log.info("Ckyc Validate Otp Encrypted Initial XML:{}", encryptedInitialXml);
        //Step 4: Encoding Encrypted Initial XML With Base64
        String encodedEncryptedInitialXml = Base64.getEncoder().encodeToString(encryptedInitialXml);
        log.info("Ckyc Validate Otp Encoded Encrypted Initial XML:{}", encodedEncryptedInitialXml);
        //Step 5: Encrypt Session Key Using CERSAI Public Key
        byte[] encryptedSessionKey = this.encryptUsingPublicKey(sessionKey);
        //Step 6: Encoding Encrypted Session Key With Base64
        String encodedEncryptedSessionKey = Base64.getEncoder().encodeToString(encryptedSessionKey);
        log.info("Ckyc Validate Otp Encoded Encrypted Session Key:{}", encodedEncryptedSessionKey);
        //Step 7: Generate Final XML With Encoded Encrypted Initial PID XML and Encoded Encrypted Session Key
        String finalXml = this.getCkycValidateOtpFinalXml(this.fiCode, requestId, encodedEncryptedInitialXml, encodedEncryptedSessionKey);
        log.info("Ckyc Validate Otp Final XML:{}", finalXml);
        //Step 8: Sign Final XML With FI Private Key
        String signedFinalXml = signXML(finalXml, false);
        return signedFinalXml;
    }

    public String getCkycValidateOtpDecryptedResponse() {
        return "OK";
    }

    public String getCkycValidateOtpFinalXml(String fiCode, String requestId, String encodedEncryptedInitialXml, String encodedEncryptedSessionKey) {
        String timestamp = generateTimestamp();
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + "<CKYC_DOWNLOAD_REQUEST>" + "<HEADER><FI_CODE>%s</FI_CODE><REQUEST_ID>%s</REQUEST_ID><VERSION>1.3</VERSION></HEADER>" + "<CKYC_INQ><SESSION_KEY>%s</SESSION_KEY><PID>%s</PID></CKYC_INQ>" + "</CKYC_DOWNLOAD_REQUEST>", fiCode, requestId, encodedEncryptedSessionKey, encodedEncryptedInitialXml);
    }

    private byte[] getInitialXmlForCkycValidateOtp(String otp, String validationFlag) {
        String timestamp = generateTimestamp();
        return String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + "<PID_DATA><DATE_TIME>%s</DATE_TIME><OTP>%s</OTP><VALIDATE>%s</VALIDATE></PID_DATA>", timestamp, otp, validationFlag).getBytes(StandardCharsets.UTF_8);
    }


    public byte[] getXmlWithTimestamp(String xml, String timestamp) {
        // Implement XML manipulation to add timestamp
        // You may want to use a proper XML parser like JAXB or DOM for this
        return xml.replace("<PID_DATA>", "<PID_DATA><DATE_TIME>" + timestamp + "</DATE_TIME>").getBytes(StandardCharsets.UTF_8);
    }

    public byte[] getXmlWithTimestamp(String xml) {
        // Implement XML manipulation to add timestamp
        // You may want to use a proper XML parser like JAXB or DOM for this
        String timestamp = generateTimestamp();
        return xml.replace("<PID_DATA>", "<PID_DATA><DATE_TIME>" + timestamp + "</DATE_TIME>").getBytes(StandardCharsets.UTF_8);
    }

    public byte[] generateSessionKey() throws NoSuchAlgorithmException, NoSuchProviderException {
        KeyGenerator kgen = KeyGenerator.getInstance("AES", "BC");
        kgen.init(256);
        SecretKey key = kgen.generateKey();
        byte[] symmKey = key.getEncoded();
        return symmKey;
    }

    public byte[] encryptUsingSessionKey(byte[] skey, byte[] data) throws InvalidCipherTextException {
        PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new AESEngine(), new PKCS7Padding());
        KeyParameter key = new KeyParameter(skey);
        cipher.init(true, key);

        int outputSize = cipher.getOutputSize(data.length);

        byte[] tempOP = new byte[outputSize];
        int processLen = cipher.processBytes(data, 0, data.length, tempOP, 0);
        int outputLen = cipher.doFinal(tempOP, processLen);

        byte[] result = new byte[processLen + outputLen];
        System.arraycopy(tempOP, 0, result, 0, result.length);
        return result;
    }

    public byte[] decryptUsingSessionKey(byte[] skey, byte[] data) throws InvalidCipherTextException {

        PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(new AESEngine(), new PKCS7Padding());
        KeyParameter key = new KeyParameter(skey);
        cipher.init(false, key);

        int outputSize = cipher.getOutputSize(data.length);

        byte[] tempOP = new byte[outputSize];
        int processLen = cipher.processBytes(data, 0, data.length, tempOP, 0);
        int outputLen = cipher.doFinal(tempOP, processLen);

        byte[] result = new byte[processLen + outputLen];
        System.arraycopy(tempOP, 0, result, 0, result.length);
        return result;
    }


    public byte[] base64Encode(byte[] data) {
        return Base64.getEncoder().encode(data);
    }


    public String base64EncodeString(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }

    public byte[] base64Decode(String data) {
        return Base64.getDecoder().decode(data);
    }

    public String base64DecodeToString(byte[] data) {
        return convertBytesToString(Base64.getDecoder().decode(data));
    }

    public String base64DecodeToString(String data) {
        return convertBytesToString(Base64.getDecoder().decode(data));
    }


    public byte[] encryptUsingPublicKey(byte[] data) throws IOException, GeneralSecurityException {
        // has to validate with XML version no. in header
        //versionNo == XML version no.
        Cipher pkCipher = Cipher.getInstance("RSA/NONE/OAEPWithSHA1AndMGF1Padding");


        if (apiVersion.equals("1.2")) {
            pkCipher = Cipher.getInstance("RSA/NONE/OAEPWithSHA1AndMGF1Padding");
        } else if (apiVersion.equals("1.3")) {
            pkCipher = Cipher.getInstance("RSA/NONE/OAEPWithSHA256AndMGF1Padding");
        } else {
            pkCipher = Cipher.getInstance("RSA/NONE/PKCS1Padding");
        }
        pkCipher.init(1, this.cersaiPublicKey);
        byte[] encSessionKey = pkCipher.doFinal(data);
        return encSessionKey;
    }

    public String signXML(String xmlDocument, boolean includeKeyInfo) {

        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            Document inputDocument = dbf.newDocumentBuilder().parse(new InputSource(new StringReader(xmlDocument)));

            Document signedDocument;

            if (apiVersion.equals("1.2")) {
                signedDocument = signV12(inputDocument, includeKeyInfo);
            } else if (apiVersion.equals("1.3")) {
                signedDocument = signV13(inputDocument, includeKeyInfo);
            } else {
                throw new RuntimeException("Invalid XML version no.: " + apiVersion);
            }

            StringWriter stringWriter = new StringWriter();
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer trans = tf.newTransformer();
            trans.transform(new DOMSource(signedDocument), new StreamResult(stringWriter));

            return stringWriter.getBuffer().toString();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Error while digitally signing the XML document", e);
        }
    }

    private Document signV13(Document xmlDoc, boolean includeKeyInfo) throws Exception {
        if (System.getenv("SKIP_DIGITAL_SIGNATURE") != null) {
            return xmlDoc;
        }

        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        // Create Reference with correct digest method URI
        Reference ref = fac.newReference("", fac.newDigestMethod("http://www.w3.org/2001/04/xmlenc#sha256", null), Collections.singletonList(fac.newTransform("http://www.w3.org/2000/09/xmldsig#enveloped-signature", (XMLStructure) null)), null, null);

        // Create SignedInfo with correct canonicalization and signature methods
        SignedInfo sInfo = fac.newSignedInfo(fac.newCanonicalizationMethod("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (XMLStructure) null), fac.newSignatureMethod("http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", null), Collections.singletonList(ref));

        if (this.privateKeyEntry == null) {
            throw new RuntimeException("Key could not be read for digital signature. Please check value of signature alias and signature password, and restart the Auth Client");
        }

        X509Certificate x509Cert = (X509Certificate) this.privateKeyEntry.getCertificate();

        // Only include KeyInfo if specifically requested
        KeyInfo kInfo = includeKeyInfo ? getKeyInfo(x509Cert, fac) : null;

        // Create DOM signing context
        DOMSignContext dsc = new DOMSignContext(this.privateKeyEntry.getPrivateKey(), xmlDoc.getDocumentElement());

        // Set namespace prefix for cleaner output (optional)
        dsc.setDefaultNamespacePrefix("ds");

        // Create and sign the XML signature
        XMLSignature signature = fac.newXMLSignature(sInfo, kInfo);
        signature.sign(dsc);

        return xmlDoc;
    }

    // Helper method to ensure proper document structure
    private Document normalizeDocument(Document doc) throws Exception {
        // Normalize the document to ensure consistent formatting
        doc.getDocumentElement().normalize();

        // Optional: Remove unnecessary whitespace
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
        transformer.setOutputProperty(OutputKeys.INDENT, "no");

        DOMSource source = new DOMSource(doc);
        StreamResult result = new StreamResult(new StringWriter());
        transformer.transform(source, result);

        // Parse back to document if needed for further processing
        return doc;
    }

    private Document signV12(Document xmlDoc, boolean includeKeyInfo) throws Exception {
        if (System.getenv("SKIP_DIGITAL_SIGNATURE") != null) {
            return xmlDoc;
        }

        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");

        Reference ref = fac.newReference("", fac.newDigestMethod("http://www.w3.org/2000/09/xmldsig#sha1", null), Collections.singletonList(fac.newTransform("http://www.w3.org/2000/09/xmldsig#enveloped-signature", (XMLStructure) null)), null, null);

        SignedInfo sInfo = fac.newSignedInfo(fac.newCanonicalizationMethod("http://www.w3.org/TR/2001/REC-xml-c14n-20010315", (XMLStructure) null), fac.newSignatureMethod("http://www.w3.org/2000/09/xmldsig#rsa-sha1", null), Collections.singletonList(ref));

        if (this.privateKeyEntry == null) {
            throw new RuntimeException("Key could not be read for digital signature. Please check value of signature alias and signature password, and restart the Auth Client");
        }

        X509Certificate x509Cert = (X509Certificate) this.privateKeyEntry.getCertificate();

        KeyInfo kInfo = getKeyInfo(x509Cert, fac);
        DOMSignContext dsc = new DOMSignContext(this.privateKeyEntry.getPrivateKey(), xmlDoc.getDocumentElement());
        XMLSignature signature = fac.newXMLSignature(sInfo, includeKeyInfo ? kInfo : null);
        signature.sign(dsc);

        Node node = dsc.getParent();
        return node.getOwnerDocument();
    }

    private KeyInfo getKeyInfo(X509Certificate cert, XMLSignatureFactory fac) {
        KeyInfoFactory kif = fac.getKeyInfoFactory();
        List x509Content = new ArrayList();
        x509Content.add(cert.getSubjectX500Principal().getName());
        x509Content.add(cert);
        X509Data xd = kif.newX509Data(x509Content);
        return kif.newKeyInfo(Collections.singletonList(xd));
    }

    public PublicKey getCersaiPublicKey(String cersaiPublicKeyFilePath) {
        FileInputStream fileInputStream = null;
        try {
            CertificateFactory certFactory = CertificateFactory.getInstance("X.509", "BC");
            fileInputStream = new FileInputStream(cersaiPublicKeyFilePath);
            X509Certificate cert = (X509Certificate) certFactory.generateCertificate(fileInputStream);
            return cert.getPublicKey();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Could not intialize encryption module", e);
        } finally {
            if (fileInputStream != null) try {
                fileInputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public KeyStore.PrivateKeyEntry getPrivateKeyFromKeyStore(String keyStoreFile, String keyStorePassword, String alias) {
        FileInputStream keyFileStream = null;
        try {
            char[] keyStorePasswordCharArray = keyStorePassword.toCharArray();
            KeyStore ks = KeyStore.getInstance("PKCS12");
            keyFileStream = new FileInputStream(keyStoreFile);
            ks.load(keyFileStream, keyStorePasswordCharArray);

            if (alias == null) {
                Enumeration alias1 = ks.aliases();
                if (ks.size() > 1) {
                    throw new Exception("More that 1 Keys found in the key store. Please specify Key Alias property!");
                }

                alias1.hasMoreElements();
                alias = (String) alias1.nextElement();
                System.out.println("Alias: " + alias);
            }
            KeyStore.PrivateKeyEntry entry = (KeyStore.PrivateKeyEntry) ks.getEntry(alias, new KeyStore.PasswordProtection(keyStorePasswordCharArray));
            KeyStore.PrivateKeyEntry localPrivateKeyEntry1 = entry;
            return localPrivateKeyEntry1;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (keyFileStream != null) try {
                keyFileStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public KeyStore.PrivateKeyEntry getPrivateKeyFromDongle(String pkcs11ConfigFilePath, String keyStorePassword, String privateKeyAlias) throws CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException, UnrecoverableEntryException {
        try {
            char[] keyStorePasswordCharArray = keyStorePassword.toCharArray();
            // Load the PKCS#11 configuration file
//            String configName = "/home/<USER>/Arihant/git/oneclick-java/ckyc/resources/pkcs11.cfg";
            Provider p = Security.getProvider("SunPKCS11");
            p = p.configure(pkcs11ConfigFilePath);
            Security.addProvider(p);

            this.currentProvider = p;
            // Initialize the KeyStore for PKCS#11
            KeyStore keyStore = KeyStore.getInstance("PKCS11", p);
            keyStore.load(null, keyStorePasswordCharArray);

            Enumeration alias = keyStore.aliases();

//            String signAlias = "KARTIKEYA SHUKLA's e-Mudhra Sub CA for Class 3 Organisation 2022 ID";
//            String signAlias = "Rohit20250226153639024";

            //Working for CKYC Search verify (rohit maker)
            String signAlias = "Rohit20250226153611374";


//            while (alias.hasMoreElements()) {
//                String aliasName = (String) alias.nextElement();
//                System.out.println(aliasName);
//                X509Certificate cert = (X509Certificate) keyStore.getCertificate(aliasName);
//                boolean[] keyUsage = cert.getKeyUsage();
//                for (int i = 0; i < keyUsage.length; i++) {
//                    if (((i == 0) || (i == 1)) && (keyUsage[i] != false)) {
//                        signAlias = aliasName;
//                        break;
//                    }
//                }
//            }

            System.out.println("Private Key Alias: " + signAlias);
            return (KeyStore.PrivateKeyEntry) keyStore.getEntry(signAlias, new KeyStore.PasswordProtection(keyStorePasswordCharArray));
        } catch (Exception e) {
            System.out.println(e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Minifies XML by removing whitespace and newlines using proper XML parsing
     */
    public String minifyXml(String xmlResponse) {
        try {
            // Parse the XML
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(xmlResponse)));

            // Transform to minified string
            TransformerFactory transformerFactory = TransformerFactory.newInstance();
            Transformer transformer = transformerFactory.newTransformer();

            // Remove formatting
            transformer.setOutputProperty(OutputKeys.INDENT, "no");
            transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no");
            transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "0");

            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(writer));

            // Get minified XML as single line
            return writer.toString().replaceAll("\\s+(?=<)", "").replaceAll(">\\s+<", "><");

        } catch (Exception e) {
            throw new RuntimeException("Error minifying XML: " + e.getMessage(), e);
        }
    }

    /**
     * Simple XML minification using regex (faster but less robust)
     */
    public String simpleMinifyXml(String xmlResponse) {
        return xmlResponse.replaceAll(">\\s+<", "><")  // Remove whitespace between tags
                .replaceAll("\\n", "")       // Remove newlines
                .replaceAll("\\r", "")       // Remove carriage returns
                .replaceAll("\\t", "")       // Remove tabs
                .trim();                     // Remove leading/trailing whitespace
    }

    /**
     * HTML encodes the given string
     */
    public String htmlEncode(String input) {
        return HtmlUtils.htmlEscape(input);
    }

    /**
     * Convenience method that combines minification and HTML encoding
     */
    public String minifyAndEncodeXml(String xmlResponse) {
        String minified = minifyXml(xmlResponse);
        return htmlEncode(minified);
    }

    /**
     * Convenience method using simple minification and HTML encoding
     */
    public String simpleMinifyAndEncodeXml(String xmlResponse) {
        String minified = simpleMinifyXml(xmlResponse);
        return htmlEncode(minified);
    }

    public Document convertXmlStringToDocument(String xmlString) throws Exception {

        // Decode HTML entities
//        String decodedXml = StringEscapeUtils.unescapeHtml4(xmlString);
        String decodedXml = HtmlUtils.htmlUnescape(xmlString);

        // Trim leading and trailing whitespace
        decodedXml = decodedXml.trim();

        // Check if the XML starts with the XML declaration
        if (!decodedXml.startsWith("<?xml")) {
            throw new Exception("Invalid XML: Missing XML declaration");
        }

        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        DocumentBuilder db = dbf.newDocumentBuilder();
        //return db.parse(new InputSource(new StringReader(xmlString)));
        return db.parse(new ByteArrayInputStream(decodedXml.getBytes(StandardCharsets.UTF_8)));
    }

    public byte[] getSessionKey(Document doc) throws Exception {
        NodeList nl = doc.getElementsByTagName("SESSION_KEY");

        if (nl.getLength() == 0) {
            throw new Exception("No Session Key tag found, document is discarded");
        }

        String base64SessionKey = nl.item(0).getTextContent();
        log.info("Base64 encoded session key: {}", base64SessionKey);
        byte[] encSessionKey = Base64.getDecoder().decode(base64SessionKey);
        log.info("Base64 decoded session key byte length: {}", encSessionKey.length);

        byte[] decryptedSessionKeyBytes = decryptUsingPrivateKey(encSessionKey);
//        byte[] decryptedSessionKeyBytes = decryptUsingPrivateKeyHybrid(encSessionKey);

//        byte[] decryptedSessionKeyBytes = tryRawDecryptAndOAEP(encSessionKey);

        log.info("Decrypted session key bytes length: {}", decryptedSessionKeyBytes.length);
        return decryptedSessionKeyBytes;

    }

    private byte[] tryRawDecryptAndOAEP(byte[] encryptedKey) throws Exception {
        PrivateKey privateKey = this.privateKeyEntry.getPrivateKey();


        // Raw decrypt with PKCS#11
        Cipher cipher = Cipher.getInstance("RSA/ECB/NoPadding", currentProvider);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] rawDecrypted = cipher.doFinal(encryptedKey);

        // Manual OAEP using BouncyCastle's built-in method
        return performOAEPUnpadding(rawDecrypted);
    }

    private byte[] performOAEPUnpadding(byte[] paddedData) throws Exception {
        // Use BouncyCastle's OAEP utility directly
        OAEPParameterSpec oaepSpec = new OAEPParameterSpec("SHA-1",           // Hash algorithm
                "MGF1",              // Mask generation function
                MGF1ParameterSpec.SHA1,  // MGF1 hash
                PSource.PSpecified.DEFAULT // No label
        );

        // This approach doesn't require private key parameters
        // but needs the modulus size information
        return unpadOAEPData(paddedData, oaepSpec);
    }

    /**
     * Manual OAEP unpadding implementation for cases where hardware tokens
     * only support raw RSA decryption
     */
    private byte[] unpadOAEPData(byte[] paddedData, OAEPParameterSpec oaepSpec) throws Exception {
        log.debug("Starting OAEP unpadding, data length: " + paddedData.length);

        // Method 1: Try BouncyCastle's OAEP implementation (recommended)
        try {
            return unpadWithBouncyCastle(paddedData, oaepSpec);
        } catch (Exception e) {
            log.warn("BouncyCastle OAEP unpadding failed: " + e.getMessage());

            // Method 2: Manual OAEP implementation as fallback
            try {
                return unpadManualOAEP(paddedData, oaepSpec);
            } catch (Exception e2) {
                log.error("Manual OAEP unpadding also failed: " + e2.getMessage());

                // Method 3: Try with different assumptions about the data format
                return unpadOAEPWithDiagnostics(paddedData, oaepSpec);
            }
        }
    }

    /**
     * Use BouncyCastle's built-in OAEP implementation
     */
    private byte[] unpadWithBouncyCastle(byte[] paddedData, OAEPParameterSpec oaepSpec) throws Exception {
        log.debug("Attempting BouncyCastle OAEP unpadding");

        // Create OAEP decoder with appropriate parameters
        OAEPEncoding oaepDecoder = new OAEPEncoding(new RSAEngine(), new SHA1Digest(),  // Hash digest
                new SHA1Digest(),  // MGF1 digest
                null                 // No encoding parameters (label)
        );

        // We don't actually need real RSA parameters for just unpadding
        // Create minimal parameters just to satisfy the API
        int modulusSize = paddedData.length;
        log.debug("Using modulus size: " + modulusSize + " bytes");

        // Try a different approach - use the actual modulus size in bits
//        BigInteger modulus = BigInteger.valueOf(2).pow(modulusSize * 8 - 1);
//        RSAKeyParameters dummyKey = new RSAKeyParameters(false, modulus, BigInteger.valueOf(65537));

        BigInteger dummyModulus = new BigInteger(1, new byte[modulusSize]);
        RSAKeyParameters dummyKey = new RSAKeyParameters(false, dummyModulus.setBit(modulusSize * 8 - 1), BigInteger.valueOf(65537));


        oaepDecoder.init(false, dummyKey); // false = decrypt mode

        byte[] result = oaepDecoder.processBlock(paddedData, 0, paddedData.length);
        log.debug("BouncyCastle OAEP unpadding successful, result length: " + result.length);
        return result;
    }

    /**
     * Manual OAEP unpadding implementation following RFC 8017
     */
    private byte[] unpadManualOAEP(byte[] paddedData, OAEPParameterSpec oaepSpec) throws Exception {
        log.debug("Starting manual OAEP unpadding");

        // OAEP parameters
        String hashAlg = oaepSpec.getDigestAlgorithm(); // Should be "SHA-256"
        MGF1ParameterSpec mgfSpec = (MGF1ParameterSpec) oaepSpec.getMGFParameters();
        byte[] label = ((PSource.PSpecified) oaepSpec.getPSource()).getValue();

        log.debug("Hash algorithm: " + hashAlg);
        log.debug("Label length: " + (label != null ? label.length : 0));

        // Initialize hash function
        MessageDigest hash = MessageDigest.getInstance(hashAlg);
        int hLen = hash.getDigestLength(); // 32 for SHA-256
        int k = paddedData.length; // Modulus length in bytes

        log.debug("Hash length: " + hLen + ", Modulus length: " + k);

        // Validate input length
        if (k < 2 * hLen + 2) {
            throw new Exception("Decryption error: invalid ciphertext length. Expected >= " + (2 * hLen + 2) + ", got " + k);
        }

        // Step 1: Length checking already done above

        // Step 2: RSA decryption already performed (paddedData is the result)

        // Step 3: OAEP decoding
        byte[] EM = paddedData;

        // Step 3a: Hash the label
        byte[] lHash = hash.digest(label != null ? label : new byte[0]);
        log.debug("Label hash: " + bytesToHex(lHash));

        // Step 3b: Separate the encoded message EM
        byte Y = EM[0];
        byte[] maskedSeed = java.util.Arrays.copyOfRange(EM, 1, hLen + 1);
        byte[] maskedDB = java.util.Arrays.copyOfRange(EM, hLen + 1, k);

        log.debug("Y: " + Y + ", maskedSeed length: " + maskedSeed.length + ", maskedDB length: " + maskedDB.length);

        // Step 3c: seedMask = MGF(maskedDB, hLen)
        byte[] seedMask = mgf1(maskedDB, hLen, hash);

        // Step 3d: seed = maskedSeed XOR seedMask
        byte[] seed = xor(maskedSeed, seedMask);

        // Step 3e: dbMask = MGF(seed, k - hLen - 1)
        byte[] dbMask = mgf1(seed, k - hLen - 1, hash);

        // Step 3f: DB = maskedDB XOR dbMask
        byte[] DB = xor(maskedDB, dbMask);

        // Step 3g: Separate DB = lHash' || PS || 0x01 || M
        byte[] lHashPrime = java.util.Arrays.copyOfRange(DB, 0, hLen);

        log.debug("Expected lHash: " + bytesToHex(lHash));
        log.debug("Actual lHash': " + bytesToHex(lHashPrime));

        // Verify lHash' equals lHash
        if (!MessageDigest.isEqual(lHash, lHashPrime)) {
            log.error("Hash mismatch - this often indicates wrong encryption parameters or corrupted data");
            throw new Exception("OAEP decoding error: hash mismatch");
        }

        // Find the 0x01 separator
        int separatorIndex = -1;
        for (int i = hLen; i < DB.length; i++) {
            if (DB[i] == 0x01) {
                separatorIndex = i;
                break;
            } else if (DB[i] != 0x00) {
                log.error("Invalid padding byte at position " + i + ": " + DB[i]);
                throw new Exception("OAEP decoding error: invalid padding");
            }
        }

        if (separatorIndex == -1) {
            log.error("Separator 0x01 not found in padding");
            throw new Exception("OAEP decoding error: separator not found");
        }

        log.debug("Separator found at position: " + separatorIndex);


        // Check Y is zero
        if (Y != 0) {
            log.error("Leading byte Y is not zero: " + Y);
            throw new Exception("OAEP decoding error: invalid leading byte");
        }

        // Extract the message
        byte[] result = java.util.Arrays.copyOfRange(DB, separatorIndex + 1, DB.length);
        log.debug("Manual OAEP unpadding successful, result length: " + result.length);
        return result;
    }

    /**
     * MGF1 mask generation function as defined in RFC 8017
     */
    private byte[] mgf1(byte[] seed, int maskLen, MessageDigest hash) throws Exception {
        int hLen = hash.getDigestLength();
        byte[] mask = new byte[maskLen];
        int counter = 0;
        int offset = 0;

        while (offset < maskLen) {
            // Convert counter to 4-byte big-endian representation
            byte[] C = new byte[4];
            C[0] = (byte) (counter >>> 24);
            C[1] = (byte) (counter >>> 16);
            C[2] = (byte) (counter >>> 8);
            C[3] = (byte) counter;

            // Hash seed || C
            hash.reset();
            hash.update(seed);
            hash.update(C);
            byte[] hashResult = hash.digest();

            // Copy appropriate number of bytes to mask
            int bytesToCopy = Math.min(hLen, maskLen - offset);
            System.arraycopy(hashResult, 0, mask, offset, bytesToCopy);

            offset += bytesToCopy;
            counter++;
        }

        return mask;
    }

    /**
     * XOR two byte arrays
     */
    private byte[] xor(byte[] a, byte[] b) {
        if (a.length != b.length) {
            throw new IllegalArgumentException("Arrays must have same length");
        }

        byte[] result = new byte[a.length];
        for (int i = 0; i < a.length; i++) {
            result[i] = (byte) (a[i] ^ b[i]);
        }
        return result;
    }

    /**
     * Enhanced OAEP unpadding with detailed diagnostics
     */
    private byte[] unpadOAEPWithDiagnostics(byte[] paddedData, OAEPParameterSpec oaepSpec) throws Exception {
        log.info("Starting diagnostic OAEP unpadding");

        // Log the raw data for analysis
        log.debug("Raw padded data (first 64 bytes): " + bytesToHex(java.util.Arrays.copyOf(paddedData, Math.min(64, paddedData.length))));

        // Try different approaches based on common issues

        // 1. Check if the data might be in wrong byte order
        try {
            byte[] reversedData = reverseBytes(paddedData);
            return unpadManualOAEP(reversedData, oaepSpec);
        } catch (Exception e) {
            log.debug("Reversed byte order attempt failed: " + e.getMessage());
        }

        // 2. Try with SHA-1 instead of SHA-256 (common mismatch)
        try {
            OAEPParameterSpec sha1Spec = new OAEPParameterSpec("SHA-1", "MGF1", MGF1ParameterSpec.SHA1, PSource.PSpecified.DEFAULT);
            return unpadManualOAEPWithSpec(paddedData, sha1Spec);
        } catch (Exception e) {
            log.debug("SHA-1 attempt failed: " + e.getMessage());
        }

        // 3. Try without any label verification (in case label was used during encryption)
        try {
            return unpadOAEPIgnoreLabel(paddedData, oaepSpec);
        } catch (Exception e) {
            log.debug("Ignore label attempt failed: " + e.getMessage());
        }

        throw new Exception("All OAEP unpadding attempts failed. " + "This suggests a fundamental mismatch in encryption/decryption parameters " + "or the data may be corrupted.");
    }

    private byte[] unpadManualOAEPWithSpec(byte[] paddedData, OAEPParameterSpec spec) throws Exception {
        // Same as unpadManualOAEP but with different spec
        String hashAlg = spec.getDigestAlgorithm();
        MessageDigest hash = MessageDigest.getInstance(hashAlg);
        int hLen = hash.getDigestLength();
        int k = paddedData.length;

        if (k < 2 * hLen + 2) {
            throw new Exception("Invalid ciphertext length for " + hashAlg);
        }

        byte[] EM = paddedData;
        byte[] lHash = hash.digest(new byte[0]); // Empty label

        byte Y = EM[0];
        byte[] maskedSeed = java.util.Arrays.copyOfRange(EM, 1, hLen + 1);
        byte[] maskedDB = java.util.Arrays.copyOfRange(EM, hLen + 1, k);

        byte[] seedMask = mgf1(maskedDB, hLen, hash);
        byte[] seed = xor(maskedSeed, seedMask);
        byte[] dbMask = mgf1(seed, k - hLen - 1, hash);
        byte[] DB = xor(maskedDB, dbMask);

        byte[] lHashPrime = java.util.Arrays.copyOfRange(DB, 0, hLen);

        if (!java.util.Arrays.equals(lHash, lHashPrime)) {
            throw new Exception("Hash mismatch with " + hashAlg);
        }

        int separatorIndex = -1;
        for (int i = hLen; i < DB.length; i++) {
            if (DB[i] == 0x01) {
                separatorIndex = i;
                break;
            } else if (DB[i] != 0x00) {
                throw new Exception("Invalid padding with " + hashAlg);
            }
        }

        if (separatorIndex == -1 || Y != 0) {
            throw new Exception("Structure error with " + hashAlg);
        }

        return java.util.Arrays.copyOfRange(DB, separatorIndex + 1, DB.length);
    }

    private byte[] unpadOAEPIgnoreLabel(byte[] paddedData, OAEPParameterSpec oaepSpec) throws Exception {
        // Try to unpad without strict label verification
        log.debug("Attempting OAEP unpadding ignoring label hash mismatch");

        String hashAlg = oaepSpec.getDigestAlgorithm();
        MessageDigest hash = MessageDigest.getInstance(hashAlg);
        int hLen = hash.getDigestLength();
        int k = paddedData.length;

        byte[] EM = paddedData;
        byte Y = EM[0];
        byte[] maskedSeed = java.util.Arrays.copyOfRange(EM, 1, hLen + 1);
        byte[] maskedDB = java.util.Arrays.copyOfRange(EM, hLen + 1, k);

        byte[] seedMask = mgf1(maskedDB, hLen, hash);
        byte[] seed = xor(maskedSeed, seedMask);
        byte[] dbMask = mgf1(seed, k - hLen - 1, hash);
        byte[] DB = xor(maskedDB, dbMask);

        // Skip label hash verification, just look for the separator
        int separatorIndex = -1;
        for (int i = hLen; i < DB.length; i++) {
            if (DB[i] == 0x01) {
                // Verify there are only zeros before this point (after the hash area)
                boolean validPadding = true;
                for (int j = hLen; j < i; j++) {
                    if (DB[j] != 0x00) {
                        validPadding = false;
                        break;
                    }
                }
                if (validPadding) {
                    separatorIndex = i;
                    break;
                }
            }
        }

        if (separatorIndex == -1 || Y != 0) {
            throw new Exception("Structure error even ignoring label hash");
        }

        log.warn("OAEP unpadding succeeded by ignoring label hash - this may indicate parameter mismatch");
        return java.util.Arrays.copyOfRange(DB, separatorIndex + 1, DB.length);
    }

    private byte[] reverseBytes(byte[] data) {
        byte[] reversed = new byte[data.length];
        for (int i = 0; i < data.length; i++) {
            reversed[i] = data[data.length - 1 - i];
        }
        return reversed;
    }

    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    public byte[] decryptUsingPrivateKeyHybrid(byte[] encryptedKey) throws Exception {

        try {
            // First, try to get the private key parameters from HSM
            // This may or may not work depending on HSM configuration
            PrivateKey key = this.privateKeyEntry.getPrivateKey();
            RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) key;

            // Create software-based private key with same parameters
            KeyFactory keyFactory = KeyFactory.getInstance("RSA", "SunJCE");
            RSAPrivateKeySpec keySpec = new RSAPrivateKeySpec(rsaPrivateKey.getModulus(), rsaPrivateKey.getPrivateExponent());
            PrivateKey softwarePrivateKey = keyFactory.generatePrivate(keySpec);

            // Use software crypto for OAEP decryption
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA256AndMGF1Padding", "SunJCE");
            cipher.init(Cipher.DECRYPT_MODE, softwarePrivateKey);
            return cipher.doFinal(encryptedKey);

        } catch (Exception e) {
            log.error("Hybrid approach failed: " + e.getMessage());
            throw new Exception("Cannot perform OAEP decryption - HSM limitation", e);
        }
    }

    public byte[] decryptUsingPrivateKey(byte[] data) throws IOException, GeneralSecurityException {
        byte[] sessionKey = null;
        try {

            log.info("provider:{}", currentProvider.getName());
            // has to validate with XML version no. in header
            //versionNo == XML version no.
            PrivateKey key = this.privateKeyEntry.getPrivateKey();


            Cipher pkCipher = Cipher.getInstance("RSA/None/OAEPWithSHA256AndMGF1Padding");

            if (apiVersion.equals("1.2")) {
//                pkCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", currentProvider);
//                pkCipher = Cipher.getInstance("RSA/NONE/PKCS1Padding", currentProvider);
//                pkCipher = Cipher.getInstance("RSA/ECB/NoPadding", currentProvider);
//                pkCipher = Cipher.getInstance("RSA/NONE/NoPadding", currentProvider);

                //Working with PKCS12
                pkCipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA1AndMGF1Padding", currentProvider);
            } else if (apiVersion.equals("1.3")) {
                log.debug("Using RSA/NONE/OAEPWithSHA256AndMGF1Padding");
//                pkCipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", currentProvider);
//                pkCipher = Cipher.getInstance("RSA/ECB/NoPadding", currentProvider);
//                pkCipher = Cipher.getInstance("RSA/NONE/PKCS1Padding", currentProvider);
                //Working with PKCS12
//                pkCipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA256AndMGF1Padding", currentProvider);
                pkCipher = Cipher.getInstance("RSA/NONE/OAEPWithSHA256AndMGF1Padding");
            } else {
                pkCipher = Cipher.getInstance("RSA/NONE/PKCS1Padding");
            }

            log.info("Cipher:{},{}", pkCipher, data.length);
            pkCipher.init(Cipher.DECRYPT_MODE, key);

            sessionKey = pkCipher.doFinal(data);
        } catch (InvalidKeyException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (NoSuchPaddingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IllegalBlockSizeException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (BadPaddingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return sessionKey;
    }

//    private byte[] removeOAEPPadding(byte[] paddedData) throws Exception {
//        // Implement OAEP unpadding algorithm
//        // This is complex - consider using BouncyCastle for this
//
//        try {
//            // Using BouncyCastle for OAEP unpadding
//            OAEPEncoding oaep = new OAEPEncoding(new RSAEngine(), new SHA256Digest());
//            oaep.init(false, new RSAKeyParameters(false,
//                    ((RSAPrivateKey) privateKey).getModulus(),
//                    ((RSAPrivateKey) privateKey).getPrivateExponent()));
//
//            return oaep.processBlock(paddedData, 0, paddedData.length);
//        } catch (Exception e) {
//            throw new Exception("OAEP unpadding failed", e);
//        }
//    }

    public String getDataFromXMLDocument(Document doc, String tag) throws Exception {
        NodeList nl = doc.getElementsByTagName(tag);


        if (nl.getLength() == 0) {
            throw new Exception("Provide tag not found, document is discarded");
        }

        return nl.item(0).getTextContent();
    }


    public byte[] getPIDData(Document doc, byte[] sessionKey) throws Exception {
        String bse64PIDData = getDataFromXMLDocument(doc, "PID");

        byte[] pidData = Base64.getDecoder().decode(bse64PIDData);
        byte[] decryptedPIDData = decryptUsingSessionKey(sessionKey, pidData);

        return decryptedPIDData;
    }

    public String convertBytesToString(byte[] bytes) {
        return new String(bytes, StandardCharsets.UTF_8);
    }

    public boolean verifySignatureWithPublicKey(String xmlContent) throws Exception {

        // Parse XML document
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        Document doc = dbf.newDocumentBuilder().parse(new ByteArrayInputStream(xmlContent.getBytes()));

        // Find Signature element
        NodeList nl = doc.getElementsByTagNameNS(XMLSignature.XMLNS, "Signature");
        if (nl.getLength() == 0) {
            throw new Exception("Cannot find Signature element");
        }

        // Create XMLSignatureFactory and unmarshal signature
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");
        XMLSignature signature = fac.unmarshalXMLSignature(new DOMStructure(nl.item(0)));

        // Create validation context
        DOMValidateContext valContext = new DOMValidateContext(this.cersaiPublicKey, nl.item(0));

        // Validate signature
        boolean isValid = signature.validate(valContext);

        System.out.println("XML Signature validation result: " + (isValid ? "VALID" : "INVALID"));

        // Check core validation status
        boolean coreValid = signature.getSignatureValue().validate(valContext);
        System.out.println("Core signature validation: " + (coreValid ? "VALID" : "INVALID"));

        // Check each reference
        for (Object refObj : signature.getSignedInfo().getReferences()) {
            Reference ref = (Reference) refObj;
            boolean refValid = ref.validate(valContext);
            System.out.println("Reference validation: " + (refValid ? "VALID" : "INVALID"));
        }

        return isValid;
    }
}


