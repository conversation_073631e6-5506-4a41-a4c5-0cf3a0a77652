package com.setulab.oneclick.ckyc.ws.controller;

import com.setulab.oneclick.ckyc.dto.VerifyRQ;
import com.setulab.oneclick.ckyc.service.CkycVerificationServiceImpl;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api")
@RequiredArgsConstructor
public class CkycVerificationController {

    private final CkycVerificationServiceImpl ckycVerificationServiceImpl;

    @PostMapping(value = "/verify")
    public ResponseEntity<String> ckycVerify(@Valid @RequestBody VerifyRQ verifyRQ) {
        return ResponseEntity.ok(ckycVerificationServiceImpl.ckycVerify(verifyRQ));
    }
}
