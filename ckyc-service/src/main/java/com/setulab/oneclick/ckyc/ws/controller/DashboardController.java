package com.setulab.oneclick.ckyc.ws.controller;

import com.setulab.oneclick.ckyc.service.DashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final DashboardService dashboardService;

    @GetMapping(value = {"", "/"})
    public String getDashboard() {
        return dashboardService.getDashboard();
    }
}
