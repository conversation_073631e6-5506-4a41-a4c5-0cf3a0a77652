spring.application.name=ckyc-service
server.port=8082
server.shutdown=graceful

######## Catalog Service Configuration  #########
catalog.page-size=10

######## Actuator Configuration  #########
management.info.git.mode=full
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
management.tracing.enabled=false
management.tracing.sampling.probability=1.0

######## Swagger Configuration  #########
swagger.api-gateway-url=http://localhost:8080/ckyc

######## CKYC Configuration  #########
ckyc.base-url=https://www.ckycindia.in
ckyc.api-version=1.3
ckyc.key-store-type=PKCS12
ckyc.fi-code=IN0888
ckyc.cersai-public-key-file-path=ckyc_CersaiSignPublicKey.cer
ckyc.fi-certificate-key-store-file-path=ckyc_rohit.pfx
ckyc.fi-certificate-key-store-file-password=roh123
ckyc.fi-certificate-private-key-alias=te-9d800711-1c56-4a7e-8c77-d3c22ffe5da0

