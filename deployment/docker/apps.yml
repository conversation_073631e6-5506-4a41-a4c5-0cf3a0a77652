name: 'oneclick4j'
services:
  webapi-service:
    build:
      context: ./../../
      dockerfile: webapi-service/Dockerfile
    image: setulab/oneclick4j-webapi-service:latest
    container_name: webapi-service
    environment:
      - APP_PORT=8081
      - CKYC_SERVICE_URL=http://api-gateway:8080/ckyc
      - KRA_SERVICE_URL=http://api-gateway:8080/kra
      - SPRING_PROFILES_ACTIVE=dev
      #      - DB_URL=******************************************
      #      - DB_USERNAME=postgres
      #      - DB_PASSWORD=postgres
      - SWAGGER_API_GATEWAY_URL=http://api-gateway:8080/webapi
      - MANAGEMENT_TRACING_ENABLED=true
      - MANAGEMENT_ZIPKIN_TRACING_ENDPOINT=http://tempo:9411
    ports:
      - "8081:8081"
    restart: unless-stopped
    #    depends_on:
    #      catalog-db:
    #        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 700m
    labels:
      logging: "promtail"
    networks:
      - oneclick4j

  ckyc-service:
    build:
      context: ./../../
      dockerfile: ckyc-service/Dockerfile
    image: setulab/oneclick4j-ckyc-service:latest
    container_name: ckyc-service
    environment:
      - APP_PORT=8082
      - SPRING_PROFILES_ACTIVE=dev
      #      - DB_URL=*****************************************
      #      - DB_USERNAME=postgres
      #      - DB_PASSWORD=postgres
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=guest
      - RABBITMQ_PASSWORD=guest
      #      - OAUTH2_SERVER_URL=http://keycloak:9191
      - SWAGGER_API_GATEWAY_URL=http://api-gateway:8080/ckyc
      - MANAGEMENT_TRACING_ENABLED=true
      - MANAGEMENT_ZIPKIN_TRACING_ENDPOINT=http://tempo:9411
    ports:
      - "8082:8082"
    restart: unless-stopped
    #    depends_on:
    #      orders-db:
    #        condition: service_healthy
    #      bookstore-rabbitmq:
    #        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 700m
    labels:
      logging: "promtail"
    networks:
      - oneclick4j

  kra-service:
    build:
      context: ./../../
      dockerfile: kra-service/Dockerfile
    image: setulab/oneclick4j-kra-service
    container_name: kra-service
    environment:
      - APP_PORT=8083
      - SPRING_PROFILES_ACTIVE=dev
      #      - DB_URL=************************************************
      #      - DB_USERNAME=postgres
      #      - DB_PASSWORD=postgres
      - RABBITMQ_HOST=bookstore-rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USERNAME=guest
      - RABBITMQ_PASSWORD=guest
      - MAIL_HOST=mailhog
      - MAIL_PORT=1025
      - SWAGGER_API_GATEWAY_URL=http://api-gateway:8080/kra
      - MANAGEMENT_TRACING_ENABLED=true
      - MANAGEMENT_ZIPKIN_TRACING_ENDPOINT=http://tempo:9411
    ports:
      - "8083:8083"
    restart: unless-stopped
    #    depends_on:
    #      notifications-db:
    #        condition: service_healthy
    #      bookstore-rabbitmq:
    #        condition: service_healthy
    #      mailhog:
    #        condition: service_started
    deploy:
      resources:
        limits:
          memory: 700m
    labels:
      logging: "promtail"
    networks:
      - oneclick4j

  api-gateway:
    build:
      context: ./../../
      dockerfile: api-gateway/Dockerfile
    image: setulab/oneclick4j-api-gateway
    container_name: api-gateway
    environment:
      - APP_PORT=8080
      - SPRING_PROFILES_ACTIVE=dev
      - WEBAPI_SERVICE_URL=http://webapi-service:8081
      - CKYC_SERVICE_URL=http://ckyc-service:8082
      - KRA_SERVICE_URL=http://kra-service:8083
      - MANAGEMENT_TRACING_ENABLED=true
      - MANAGEMENT_ZIPKIN_TRACING_ENDPOINT=http://tempo:9411
    ports:
      - "8080:8080"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 700m
    labels:
      logging: "promtail"
    networks:
      - oneclick4j

networks:
  oneclick4j:
    external: true