name: 'oneclick4j'
services:
  postgres:
    image: postgres:17-alpine
    restart: always
    container_name: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 500m
    networks:
      - oneclick4j

  pgadmin:
    image: dpage/pgadmin4:latest
    restart: always
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_LISTEN_PORT: 80
    ports:
      - "15432:80"
    volumes:
      - pgadmin:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - oneclick4j

  bookstore-rabbitmq:
    image: rabbitmq:4.0.4-management
    restart: always
    container_name: rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "5672:5672"
      - "15672:15672"
    healthcheck:
      test: rabbitmq-diagnostics check_port_connectivity
      interval: 30s
      timeout: 30s
      retries: 10
    deploy:
      resources:
        limits:
          memory: 500m
    networks:
      - oneclick4j

  mailhog:
    image: mailhog/mailhog:v1.0.1
    container_name: mailhog
    ports:
      - "1025:1025"
      - "8025:8025"

  keycloak:
    image: quay.io/keycloak/keycloak:24.0.2
    command: [ 'start-dev', '--import-realm', '--http-port=9191' ]
    container_name: keycloak
    hostname: keycloak
    volumes:
      - ./realm-config:/opt/keycloak/data/import
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    ports:
      - "9191:9191"
    deploy:
      resources:
        limits:
          memory: 2gb
    networks:
      - oneclick4j

volumes:
  postgres:
  pgadmin:

networks:
  oneclick4j:
    external: true