package com.setulab.oneclick.kra;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
public class ApplicationProperties {

    // XML Base URL
    @Value("${kra.cvl.pancheck.xml.base-url}")
    private String cvlPancheckXmlBaseUrl;

    // XML Username
    @Value("${kra.cvl.pancheck.xml.username}")
    private String cvlPancheckXmlUsername;

    // XML Password
    @Value("${kra.cvl.pancheck.xml.password}")
    private String cvlPancheckXmlPassword;

    // XML PosCode
    @Value("${kra.cvl.pancheck.xml.poscode}")
    private String cvlPancheckXmlPosCode;

    // XML RtaCode
    @Value("${kra.cvl.pancheck.xml.rtacode}")
    private String cvlPancheckXmlRtaCode;

    // JSON User Agent
    @Value("${kra.cvl.pancheck.json.user-agent}")
    private String cvlPancheckJsonUserAgent;

    // JSON Base URL
    @Value("${kra.cvl.pancheck.json.base-url}")
    private String cvlPancheckJsonBaseUrl;

    // JSON Api Key
    @Value("${kra.cvl.pancheck.json.apikey}")
    private String cvlPancheckJsonApiKey;

    // JSON Aes Key
    @Value("${kra.cvl.pancheck.json.aeskey}")
    private String cvlPancheckJsonAesKey;

    // JSON Username
    @Value("${kra.cvl.pancheck.json.username}")
    private String cvlPancheckJsonUsername;

    // JSON Password
    @Value("${kra.cvl.pancheck.json.password}")
    private String cvlPancheckJsonPassword;

    // JSON PosCode
    @Value("${kra.cvl.pancheck.json.poscode}")
    private String cvlPancheckJsonPosCode;

    // JSON RtaCode
    @Value("${kra.cvl.pancheck.json.rtacode}")
    private String cvlPancheckJsonRtaCode;

    // JSON Token Valid Time
    @Value("${kra.cvl.pancheck.json.token-valid-time}")
    private String cvlPancheckJsonTokenValidTime;

    @PostConstruct
    public void init() {
        log.info("KRA Application Properties");
        log.info("--------------------------------");
        log.info("Cvl Pancheck Xml Properties");
        log.info("--------------------------------");
        log.info("Cvl Pancheck Xml Base URL: {}", cvlPancheckXmlBaseUrl);
        log.info("Cvl Pancheck Xml Username: {}", cvlPancheckXmlUsername);
        log.info("Cvl Pancheck Xml Password: {}", cvlPancheckXmlPassword.substring(0, 4) + "********");
        log.info("Cvl Pancheck Xml PosCode: {}", cvlPancheckXmlPosCode);
        log.info("Cvl Pancheck Xml RtaCode: {}", cvlPancheckXmlRtaCode);
        log.info("--------------------------------");
        log.info("Cvl Pancheck Json Properties");
        log.info("--------------------------------");
        log.info("Cvl Pancheck Json Base URL: {}", cvlPancheckJsonBaseUrl);
        log.info("Cvl Pancheck Json Api Key: {}", cvlPancheckJsonApiKey.substring(0, 4) + "********");
        log.info("Cvl Pancheck Json Aes Key: {}", cvlPancheckJsonAesKey.substring(0, 4) + "********");
        log.info("Cvl Pancheck Json Username: {}", cvlPancheckJsonUsername);
        log.info("Cvl Pancheck Json Password: {}", cvlPancheckJsonPassword.substring(0, 4) + "********");
        log.info("Cvl Pancheck Json PosCode: {}", cvlPancheckJsonPosCode);
        log.info("Cvl Pancheck Json RtaCode: {}", cvlPancheckJsonRtaCode);
        log.info("Cvl Pancheck Json Token Valid Time: {}", cvlPancheckJsonTokenValidTime);
        log.info("--------------------------------");
    }

}
