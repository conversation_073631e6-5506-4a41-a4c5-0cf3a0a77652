package com.setulab.oneclick.kra.client;

import com.setulab.oneclick.kra.ApplicationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Slf4j
@Configuration
public class CvlClientConfiguration {

    /**
     * RestTemplate configuration for Json API
     */
    @Bean(name = "cvlPancheckJsonRestTemplate")
    public RestTemplate cvlPancheckJsonRestTemplate(RestTemplateBuilder templateBuilder, ApplicationProperties properties) {
        return templateBuilder.rootUri(properties.getCvlPancheckJsonBaseUrl()).requestFactory(this::createRequestFactory).build();
    }

    /**
     * RestTemplate configuration for Xml API
     */
    @Bean(name = "cvlPancheckXmlRestTemplate")
    public RestTemplate cvlPancheckXmlRestTemplate(RestTemplateBuilder templateBuilder, ApplicationProperties properties) {
        return templateBuilder.rootUri(properties.getCvlPancheckXmlBaseUrl() + "/CVLPanInquiry.svc").requestFactory(this::createRequestFactory).build();
    }

    private SimpleClientHttpRequestFactory createRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(Duration.ofSeconds(30));
        factory.setReadTimeout(Duration.ofSeconds(60));
        return factory;
    }

}
