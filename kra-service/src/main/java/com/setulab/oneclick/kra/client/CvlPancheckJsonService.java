package com.setulab.oneclick.kra.client;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public class CvlPancheckJsonService {

    // Inner classes for request/response types

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CvlKraPancheckJsonGetTokenRequest {
        private String tokenValidTime = "";
    }

    @Getter
    @Setter
    public static class CvlKraPancheckJsonGetTokenResponse {
        private String success;
        private String token;
    }

    @Getter
    @Setter
    public static class CvlKraPancheckJsonGetPanStatusRequest {
        private String panNumber;

        public CvlKraPancheckJsonGetPanStatusRequest() {
        }

        public CvlKraPancheckJsonGetPanStatusRequest(String panNumber) {
            this.panNumber = panNumber;
        }

        public String getPanNumber() {
            return panNumber;
        }

        public void setPanNumber(String panNumber) {
            this.panNumber = panNumber;
        }
    }

    public static class CvlKraPancheckJsonSolicitPANDetailsFetchALLKRARequest {
        private String panNumber;
        private String dob;
        private String kraCode;
        private String fetchType;

        // Constructors
        public CvlKraPancheckJsonSolicitPANDetailsFetchALLKRARequest() {
        }

        public CvlKraPancheckJsonSolicitPANDetailsFetchALLKRARequest(String panNumber, String dob, String kraCode, String fetchType) {
            this.panNumber = panNumber;
            this.dob = dob;
            this.kraCode = kraCode;
            this.fetchType = fetchType;
        }

        // Getters and setters
        public String getPanNumber() {
            return panNumber;
        }

        public void setPanNumber(String panNumber) {
            this.panNumber = panNumber;
        }

        public String getDob() {
            return dob;
        }

        public void setDob(String dob) {
            this.dob = dob;
        }

        public String getKraCode() {
            return kraCode;
        }

        public void setKraCode(String kraCode) {
            this.kraCode = kraCode;
        }

        public String getFetchType() {
            return fetchType;
        }

        public void setFetchType(String fetchType) {
            this.fetchType = fetchType;
        }
    }

    public static class CvlKraPancheckJsonInsertUpdateKYCRecordRequest {
        private Map<String, Object> root;

        public CvlKraPancheckJsonInsertUpdateKYCRecordRequest() {
            this.root = new HashMap<>();
        }

        public Map<String, Object> getRoot() {
            return root;
        }

        public void setRoot(Map<String, Object> root) {
            this.root = root;
        }
    }

    public static class CvlKraPancheckJsonRequestOptions {
        private String token;
        private boolean xml = false;

        public CvlKraPancheckJsonRequestOptions() {
        }

        public CvlKraPancheckJsonRequestOptions(String token, boolean xml) {
            this.token = token;
            this.xml = xml;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public boolean isXml() {
            return xml;
        }

        public void setXml(boolean xml) {
            this.xml = xml;
        }
    }

    public static class ApiResponse<T> {
        private T data;
        private String error;

        public ApiResponse(T data, String error) {
            this.data = data;
            this.error = error;
        }

        public T getData() {
            return data;
        }

        public void setData(T data) {
            this.data = data;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }
    }

    // Service routes
    public static class CvlPancheckJsonServiceRoutes {
        public static final String GET_TOKEN = "/int/api/GetToken";
        public static final String GET_PAN_STATUS = "/int/api/GetPanStatus";
        public static final String SOLICIT_PAN_DETAILS_FETCH_ALL_KRA = "/int/api/SolicitPANDetailsFetchALLKRA";
        public static final String INSERT_UPDATE_KYC_RECORD = "/int/api/InsertUpdateKYCRecord";
    }

    // Instance variables
    private final String version = "";
    private final OkHttpClient httpClient;
    private final String baseURL;
    private final String userAgent;
    private final String apiKey;
    private final String aesKey;
    private final String userName;
    private final String password;
    private final String posCode;
    private final String rtaCode;
    private final String tokenValidTime;
    private final ObjectMapper objectMapper;
    private final XmlMapper xmlMapper;

    private String token = null;
    private Long tokenExpiry = null;

    // Constructor
    public CvlPancheckJsonService(String baseURL, String userAgent, String apiKey, String aesKey,
                                  String userName, String posCode, String rtaCode, String password,
                                  String tokenValidTime, String token, Long tokenExpiry) {
        this.baseURL = baseURL;
        this.userAgent = userAgent != null ? userAgent : "OneClick";
        this.apiKey = apiKey;
        this.aesKey = aesKey;
        this.userName = userName;
        this.posCode = posCode;
        this.rtaCode = rtaCode;
        this.password = password;
        this.tokenValidTime = tokenValidTime != null ? tokenValidTime : "";
        this.token = token;
        this.tokenExpiry = tokenExpiry;

        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();
        this.xmlMapper = new XmlMapper();
    }

    // Simplified constructor
    public CvlPancheckJsonService(String baseURL, String apiKey, String aesKey, String userName,
                                  String posCode, String rtaCode, String password) {
        this(baseURL, "OneClick", apiKey, aesKey, userName, posCode, rtaCode, password, "", null, null);
    }

    public CvlKraPancheckJsonGetTokenResponse getToken(CvlKraPancheckJsonGetTokenRequest request) throws Exception {
        String url = baseURL + CvlPancheckJsonServiceRoutes.GET_TOKEN;

        // Prepare payload
        Map<String, String> payload = new HashMap<>();
        payload.put("username", userName);
        payload.put("poscode", posCode);
        payload.put("password", password);

        // Generate IV and encrypt data
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(aesKey, data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        // Prepare headers
        Headers headers = new Headers.Builder()
                .add("Content-Type", "application/json")
                .add("Accept", "*/*")
                .add("content-type", "application/json; charset=utf-8")
                .add("user-agent", userAgent)
                .add("api_key", apiKey)
                .add("tokenvalidtime", request.getTokenValidTime())
                .build();

        // Make API call
        RequestBody body = RequestBody.create(encryptedRequestData, MediaType.get("application/json"));
        Request httpRequest = new Request.Builder()
                .url(url)
                .post(body)
                .headers(headers)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("HTTP error: " + response.code());
            }

            String responseBody = response.body().string();
            String[] splittedStrings = responseBody.split(":");
            String responseIv = splittedStrings[0];
            String encryptedText = splittedStrings[1];

            String decryptedResponseData = decryptString(aesKey, encryptedText, responseIv);
            if (decryptedResponseData == null) {
                throw new Exception("Failed to decrypt response data");
            }

            CvlKraPancheckJsonGetTokenResponse parsedData = objectMapper.readValue(decryptedResponseData, CvlKraPancheckJsonGetTokenResponse.class);
            System.out.println("Parsed Data: " + objectMapper.writeValueAsString(parsedData));

            if ("1".equals(parsedData.getSuccess()) && parsedData.getToken() != null) {
                this.token = parsedData.getToken();
                return parsedData;
            }

            throw new Exception(objectMapper.writeValueAsString(parsedData));
        }
    }

    public Map<String, Object> getPanStatus(CvlKraPancheckJsonGetPanStatusRequest request, CvlKraPancheckJsonRequestOptions options) throws Exception {
        String url = baseURL + CvlPancheckJsonServiceRoutes.GET_PAN_STATUS;
        String authenticationToken = options != null ? options.getToken() : null;

        if (authenticationToken == null) {
            CvlKraPancheckJsonGetTokenResponse tokenResponse = getToken(new CvlKraPancheckJsonGetTokenRequest(tokenValidTime));
            authenticationToken = tokenResponse.getToken();
        }

        // Prepare payload
        Map<String, String> payload = new HashMap<>();
        payload.put("pan", request.getPanNumber());
        payload.put("poscode", posCode);

        // Generate IV and encrypt data
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(aesKey, data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        // Prepare headers
        Headers headers = new Headers.Builder()
                .add("content-type", "application/json")
                .add("user-agent", userAgent)
                .add("Token", authenticationToken)
                .build();

        // Make API call
        RequestBody body = RequestBody.create(encryptedRequestData, MediaType.get("application/json"));
        Request httpRequest = new Request.Builder()
                .url(url)
                .post(body)
                .headers(headers)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("HTTP error: " + response.code());
            }

            String responseBody = response.body().string();
            String[] splittedStrings = responseBody.split(":");
            String responseIv = splittedStrings[0];
            String encryptedText = splittedStrings[1];

            String decryptedResponseData = decryptString(aesKey, encryptedText, responseIv);
            if (decryptedResponseData == null) {
                throw new Exception("Failed to decrypt pan status response data");
            }

            Map<String, Object> parsedData = objectMapper.readValue(decryptedResponseData, Map.class);
            Object resdtls = parsedData.get("resdtls");
            String errorCode = (String) parsedData.get("error_code");
            String errorMessage = (String) parsedData.get("error_message");

            if (resdtls != null && "".equals(errorCode) && "".equals(errorMessage)) {
                return objectMapper.readValue((String) resdtls, Map.class);
            }

            throw new Exception(errorMessage);
        }
    }

    public ApiResponse<Map<String, Object>> solicitPANDetailsFetchALLKRA(CvlKraPancheckJsonSolicitPANDetailsFetchALLKRARequest request, CvlKraPancheckJsonRequestOptions options) throws Exception {
        String url = baseURL + CvlPancheckJsonServiceRoutes.SOLICIT_PAN_DETAILS_FETCH_ALL_KRA;
        String authenticationToken = options != null ? options.getToken() : null;

        if (authenticationToken == null) {
            CvlKraPancheckJsonGetTokenResponse tokenResponse = getToken(new CvlKraPancheckJsonGetTokenRequest(tokenValidTime));
            authenticationToken = tokenResponse.getToken();
        }

        // Prepare payload
        Map<String, Object> appPanInq = new HashMap<>();
        appPanInq.put("APP_PAN_NO", request.getPanNumber());
        appPanInq.put("APP_DOB_INCORP", request.getDob());
        appPanInq.put("APP_POS_CODE", posCode);
        appPanInq.put("APP_RTA_CODE", rtaCode);
        appPanInq.put("APP_KRA_CODE", request.getKraCode());
        appPanInq.put("FETCH_TYPE", request.getFetchType());

        Map<String, Object> appReqRoot = new HashMap<>();
        appReqRoot.put("APP_PAN_INQ", appPanInq);

        Map<String, Object> payload = new HashMap<>();
        payload.put("APP_REQ_ROOT", appReqRoot);

        // Generate IV and encrypt data
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data;

        if (options != null && options.isXml()) {
            data = xmlMapper.writeValueAsString(appReqRoot);
            System.out.println("KRA Solicit PAN Details Fetch All Kra Request: " + data);
        } else {
            data = objectMapper.writeValueAsString(payload);
        }

        String encryptedData = encryptString(aesKey, data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        // Prepare headers
        Headers headers = new Headers.Builder()
                .add("content-type", "application/json")
                .add("user-agent", userAgent)
                .add("Token", authenticationToken)
                .build();

        // Make API call
        RequestBody body = RequestBody.create(encryptedRequestData, MediaType.get("application/json"));
        Request httpRequest = new Request.Builder()
                .url(url)
                .post(body)
                .headers(headers)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("HTTP error: " + response.code());
            }

            String responseBody = response.body().string();
            String[] splittedStrings = responseBody.split(":");
            String responseIv = splittedStrings[0];
            String encryptedText = splittedStrings[1];

            String decryptedResponseData = decryptString(aesKey, encryptedText, responseIv);
            if (decryptedResponseData == null) {
                throw new Exception("Failed to decrypt solicit pan details fetch all kra response data");
            }

            Map<String, Object> parsedData = objectMapper.readValue(decryptedResponseData, Map.class);
            Object resdtls = parsedData.get("resdtls");

            if (resdtls != null) {
                Map<String, Object> resultData = objectMapper.readValue((String) resdtls, Map.class);
                return new ApiResponse<>(resultData, null);
            }

            throw new Exception(objectMapper.writeValueAsString(parsedData));
        }
    }

    public ApiResponse<Map<String, Object>> insertUpdateKYCRecord(CvlKraPancheckJsonInsertUpdateKYCRecordRequest request, CvlKraPancheckJsonRequestOptions options) throws Exception {
        String url = baseURL + CvlPancheckJsonServiceRoutes.INSERT_UPDATE_KYC_RECORD;
        String authenticationToken = options != null ? options.getToken() : null;

        if (authenticationToken == null) {
            CvlKraPancheckJsonGetTokenResponse tokenResponse = getToken(new CvlKraPancheckJsonGetTokenRequest(tokenValidTime));
            authenticationToken = tokenResponse.getToken();
        }

        // Generate IV and encrypt data
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data;

        if (options != null && options.isXml()) {
            data = xmlMapper.writeValueAsString(request.getRoot());
        } else {
            data = objectMapper.writeValueAsString(request);
        }

        System.out.println("KRA Insert Update KYC Record Request: " + data);

        String encryptedData = encryptString(aesKey, data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        // Prepare headers
        Headers headers = new Headers.Builder()
                .add("content-type", "application/json")
                .add("user-agent", userAgent)
                .add("Token", authenticationToken)
                .build();

        // Make API call
        RequestBody body = RequestBody.create(encryptedRequestData, MediaType.get("application/json"));
        Request httpRequest = new Request.Builder()
                .url(url)
                .post(body)
                .headers(headers)
                .build();

        try (Response response = httpClient.newCall(httpRequest).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("HTTP error: " + response.code());
            }

            String responseBody = response.body().string();
            System.out.println("KRA Insert Update KYC Record Response Encrypted: " + responseBody);

            String[] splittedStrings = responseBody.split(":");
            String responseIv = splittedStrings[0];
            String encryptedText = splittedStrings[1];

            String decryptedResponseData = decryptString(aesKey, encryptedText, responseIv);
            System.out.println("KRA Insert Update KYC Record Response Decrypted: " + decryptedResponseData);

            if (decryptedResponseData == null) {
                throw new Exception("Failed to decrypt insert update kyc record response data");
            }

            Map<String, Object> parsedData = objectMapper.readValue(decryptedResponseData, Map.class);
            System.out.println("KRA Insert Update KYC Record Response Parsed: " + objectMapper.writeValueAsString(parsedData));

            Object resdtls = parsedData.get("resdtls");
            if (resdtls != null) {
                Map<String, Object> parsedResDtls = objectMapper.readValue((String) resdtls, Map.class);
                Map<String, Object> kycData = (Map<String, Object>) parsedResDtls.get("KYCDATA");

                if (kycData != null && !"01".equals(kycData.get("APP_STATUS"))) {
                    throw new Exception((String) kycData.get("APP_STATUS_DESC"));
                }

                return new ApiResponse<>(parsedResDtls, null);
            }

            throw new Exception(objectMapper.writeValueAsString(parsedData));
        }
    }

    // Private utility methods
    private byte[] generateIV() {
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    private String encryptString(String aesKey, String data, byte[] iv) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(aesKey);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    private String decryptString(String aesKey, String encryptedText, String iv) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(aesKey);
            byte[] ivBytes = Base64.getDecoder().decode(iv);

            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            System.err.println("Decryption error: " + e.getMessage());
            return null;
        }
    }
}
