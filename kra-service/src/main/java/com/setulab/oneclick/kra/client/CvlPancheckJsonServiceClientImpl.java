package com.setulab.oneclick.kra.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.setulab.oneclick.kra.ApplicationProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
@Service("cvlPancheckJsonServiceClient")
public class CvlPancheckJsonServiceClientImpl implements CvlPancheckJsonServiceClient {

    private String version = "";
    private RestTemplate restTemplate;
    private ApplicationProperties applicationProperties;


    public CvlPancheckJsonServiceClientImpl(@Qualifier("cvlPancheckJsonRestTemplate") RestTemplate restTemplate, ApplicationProperties applicationProperties) {
        this.restTemplate = restTemplate;
        this.applicationProperties = applicationProperties;

    }

    @Override
    public JsonNode GetToken(String passKey) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/int/api/GetToken";
        log.info("Initiating GetToken Json (RestTemplate) for URl: {}", url);

        // HTTP Request Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(List.of(MediaType.APPLICATION_JSON));
//        httpHeaders.add("user-agent", );

        return null;
    }

    @Override
    public JsonNode GetPanStatus(JsonNode payload) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/int/api/GetPanStatus";
        log.info("Initiating GetPanStatus Json (RestTemplate) for URl: {}", url);
        return null;
    }

    @Override
    public JsonNode SolicitPANDetailsFetchALLKRA(JsonNode payload) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/int/api/SolicitPANDetailsFetchALLKRA";
        log.info("Initiating SolicitPANDetailsFetchALLKRA Json (RestTemplate) for URl: {}", url);
        return null;
    }

    @Override
    public JsonNode InsertUpdateKYCRecord(JsonNode payload) {
        String url = applicationProperties.getCvlPancheckJsonBaseUrl() + "/int/api/InsertUpdateKYCRecord";
        log.info("Initiating InsertUpdateKYCRecord Json (RestTemplate) for URl: {}", url);
        return null;
    }
}
