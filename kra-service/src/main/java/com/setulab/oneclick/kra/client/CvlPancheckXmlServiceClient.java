package com.setulab.oneclick.kra.clients;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * Client interface for Cvl Pancheck Xml Service API v2.4
 * Based on Cvl Pancheck Xml API documentation
 */
public interface CvlPancheckXmlServiceClient {

    /**
     * Get Token API - Get Token for further requests
     *
     * @param passKey Request xml payload
     * @return String containing the Token response
     */
    JsonNode GetToken(String passKey);

    /**
     * Get PAN Status API - Get PAN Status for further requests
     *
     * @param panNumber Request xml payload
     * @return String containing the PAN Status response
     */
    JsonNode GetPanStatus(String panNumber);

    /**
     * Solicit PAN Details Fetch ALL KRA API - Solicit PAN Details Fetch ALL KRA for further requests
     *
     * @param fetchType Kra Data Fetch Type (I)
     * @param kraCode   Kra Code of Client
     * @param panNumber Pan Number of Client
     * @param dob       Date of Birth of Client (dd/MM/yyyy)
     * @return String containing the Solicit PAN Details Fetch ALL KRA response
     */
    JsonNode SolicitPANDetailsFetchALLKRA(String fetchType, String kraCode, String panNumber, String dob);

    /**
     * Insert/Update KYC Record API - Insert/Update KYC Record for further requests
     *
     * @param payload Request xml payload
     * @return String containing the Insert/Update KYC Record response
     */
    JsonNode InsertUpdateKYCRecord(String payload);
}
