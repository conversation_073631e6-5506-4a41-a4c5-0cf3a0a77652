package com.setulab.oneclick.kra.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.setulab.oneclick.kra.ApplicationProperties;
import com.setulab.oneclick.kra.enums.RandomNumberGenerationMethod;
import com.setulab.oneclick.kra.utils.RandomNumberUtils;
import com.setulab.oneclick.kra.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

@Slf4j
@Service("cvlPancheckXmlServiceClient")
public class CvlPancheckXmlServiceClientImpl implements CvlPancheckXmlServiceClient {

    private final RestTemplate restTemplate;
    private final ApplicationProperties applicationProperties;

    public CvlPancheckXmlServiceClientImpl(@Qualifier("cvlPancheckXmlRestTemplate") RestTemplate restTemplate, ApplicationProperties applicationProperties) {
        this.restTemplate = restTemplate;
        this.applicationProperties = applicationProperties;
    }

    @Override
    public JsonNode GetToken(String passKey) {
        String baseUrl = applicationProperties.getCvlPancheckXmlBaseUrl();
        String serviceUrl = baseUrl + "/CVLPanInquiry.svc";
        String soapAction = baseUrl + "/ICVLPanInquiry/GetPassword";
        log.info("Initiating GetToken Xml (RestTemplate) for SOAPAction: {}, passKey: {}", soapAction, passKey);

        String soapPayload = String.format("<Envelope xmlns='http://schemas.xmlsoap.org/soap/envelope/'><Body><GetPassword xmlns='%s'><webApi><password>%s</password><passKey>%s</passKey></webApi></GetPassword></Body></Envelope>", baseUrl, applicationProperties.getCvlPancheckXmlPassword(), passKey);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.add("SOAPAction", soapAction);
        log.info("GetToken Xml Request Headers: {}", headers);
        log.info("GetToken Xml Request Payload: {}", soapPayload);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(soapPayload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("GetToken Xml Response: {}", response);

        // Example: Extract specific tags from the SOAP response
        // Create Json Node with property token
        ObjectMapper objectMapper = new ObjectMapper();
        if (responseString != null) {
            // Extract APP_GET_PASS from SOAP response using recursive search
            Optional<String> appGetPass = XmlUtils.findFirstTagRecursively(responseString, "APP_GET_PASS");
            if (appGetPass.isPresent()) {
                log.info("Extracted APP_GET_PASS: {}", appGetPass.get());
                // You can return this value or process it further
                JsonNode jsonNode = objectMapper.createObjectNode().put("token", appGetPass.get()).put("passKey", passKey);
                return jsonNode;
            }
        }

        throw new RuntimeException("GetToken Xml Response is null");
    }

    @Override
    public JsonNode GetPanStatus(String panNumber) {
        String baseUrl = applicationProperties.getCvlPancheckXmlBaseUrl();
        String serviceUrl = baseUrl + "/CVLPanInquiry.svc";
        String soapAction = baseUrl + "/ICVLPanInquiry/GetPanStatus";
        log.info("Initiating GetPanStatus Xml (RestTemplate) for SOAPAction: {}, panNumber: {}", soapAction, panNumber);

        // Get Token
        String passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
        JsonNode token = this.GetToken(passKey);
        String password = token.get("token").asText();
        String soapPayload = String.format("<Envelope xmlns='http://schemas.xmlsoap.org/soap/envelope/'><Body><GetPanStatus xmlns='%s'><webApi><pan>%s</pan><userName>%s</userName><posCode>%s</posCode><password>%s</password><passKey>%s</passKey></webApi></GetPanStatus></Body></Envelope>", baseUrl, panNumber, applicationProperties.getCvlPancheckXmlUsername(), applicationProperties.getCvlPancheckXmlPosCode(), password, passKey);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.add("SOAPAction", soapAction);
        log.info("GetPanStatus Xml Request Headers: {}", headers);
        log.info("GetPanStatus Xml Request Payload: {}", soapPayload);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(soapPayload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("GetPanStatus Xml Response: {}", responseString);

        JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "APP_RES_ROOT");
        log.info("GetPanStatus Xml Response Json: {}", jsonNode);
        return jsonNode;
    }

    @Override
    public JsonNode SolicitPANDetailsFetchALLKRA(String fetchType, String kraCode, String panNumber, String dob) {
        String baseUrl = applicationProperties.getCvlPancheckXmlBaseUrl();
        String serviceUrl = baseUrl + "/CVLPanInquiry.svc";
        String soapAction = baseUrl + "/ICVLPanInquiry/SolicitPANDetailsFetchALLKRA";
        log.info("Initiating SolicitPANDetailsFetchALLKRA Xml (RestTemplate) for SOAPAction: {}, KraCode: {}, panNumber: {}, dob: {}", soapAction, kraCode, panNumber, dob);

        // Get Token
        String passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
        JsonNode token = this.GetToken(passKey);
        String password = token.get("token").asText();
        String soapPayload = String.format("<Envelope xmlns='http://schemas.xmlsoap.org/soap/envelope/'><Body><SolicitPANDetailsFetchALLKRA xmlns='%s'><webApi><inputXml><![CDATA[<APP_REQ_ROOT><APP_PAN_INQ><APP_PAN_NO>%s</APP_PAN_NO><APP_DOB_INCORP>%s</APP_DOB_INCORP><APP_POS_CODE>%s</APP_POS_CODE><APP_RTA_CODE>%s</APP_RTA_CODE><APP_KRA_CODE>%s</APP_KRA_CODE><FETCH_TYPE>%s</FETCH_TYPE></APP_PAN_INQ></APP_REQ_ROOT>]]></inputXml><userName>%s</userName><posCode>%s</posCode><password>%s</password><passKey>%s</passKey></webApi></SolicitPANDetailsFetchALLKRA></Body></Envelope>", baseUrl, panNumber, dob, applicationProperties.getCvlPancheckXmlPosCode(), applicationProperties.getCvlPancheckXmlRtaCode(), kraCode, "I", applicationProperties.getCvlPancheckXmlUsername(), applicationProperties.getCvlPancheckXmlPosCode(), password, passKey);

        // HTTP Request Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.add("SOAPAction", soapAction);
        log.info("SolicitPANDetailsFetchALLKRA Xml Request Headers: {}", headers);
        log.info("SolicitPANDetailsFetchALLKRA Xml Request Payload: {}", soapPayload);

        // HTTP Request Body
        HttpEntity<String> requestEntity = new HttpEntity<>(soapPayload, headers);

        // HTTP Request Send
        ResponseEntity<String> response = restTemplate.postForEntity(serviceUrl, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("SolicitPANDetailsFetchALLKRA Xml Response: {}", responseString);


        JsonNode appType = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "APP_TYPE");
        log.info("APP_TYPE: {}", appType);
        boolean a = appType.asText().equals("I") || appType.asText().equals("NI");
        boolean b = responseString.contains(panNumber) && responseString.contains(kraCode);
        switch (kraCode) {
            case "CVLKRA":
                log.info("Extracting CVLKRA Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml CVLKRA Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "DOTEX":
                log.info("Extracting DOTEX Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml DOTEX Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "NDML":
                log.info("Extracting NDML Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml NDML Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "CAMS":
                log.info("Extracting CAMS Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml CAMS Response Json: {}", jsonNode);
                    return jsonNode;
                }
            case "KARVY":
                log.info("Extracting KARVY Response Json: {}", responseString);
                // Check if ROOT tag exists
                if (a && b) {
                    JsonNode jsonNode = XmlUtils.extractNestedStructureAsJsonNodeClean(responseString, "ROOT");
                    log.info("SolicitPANDetailsFetchALLKRA Xml KARVY Response Json: {}", jsonNode);
                    return jsonNode;
                }
            default:
                throw new IllegalArgumentException("Invalid kraCode: " + kraCode);
        }
    }

    @Override
    public JsonNode InsertUpdateKYCRecord(String payload) {
        String baseUrl = applicationProperties.getCvlPancheckXmlBaseUrl();
        String soapAction = baseUrl + "/ICVLPanInquiry/InsertUpdateKYCRecord";
        log.info("Initiating InsertUpdateKYCRecord Xml (RestTemplate) for SOAPAction: {}", soapAction);
        return null;
    }

}
