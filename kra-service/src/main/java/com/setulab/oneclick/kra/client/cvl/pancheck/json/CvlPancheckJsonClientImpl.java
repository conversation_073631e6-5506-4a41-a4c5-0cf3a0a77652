package com.setulab.oneclick.kra.client.cvl.pancheck.json;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CvlPancheckJsonClientImpl {

    //Instance variables
    private final String version = "";
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final XmlMapper xmlMapper;

    private final CvlPancheckJsonClientConfig cvlPancheckJsonClientConfig;

    private String token = null;
    private String tokenExpiry = null;

    public CvlPancheckJsonClientImpl(CvlPancheckJsonClientConfig cvlPancheckJsonClientConfig) {
        this.cvlPancheckJsonClientConfig = cvlPancheckJsonClientConfig;
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.xmlMapper = new XmlMapper();
    }

    public JsonNode getToken(String tokenValidTime) throws Exception {
        String route = CvlPancheckJsonClientRoutes.GET_TOKEN;

        // Request Payload
        Map<String, String> payload = new HashMap<>();
        payload.put("username", cvlPancheckJsonClientConfig.getUsername());
        payload.put("poscode", cvlPancheckJsonClientConfig.getPosCode());
        payload.put("password", cvlPancheckJsonClientConfig.getPassword());

        // Http Headers
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.setAccept(List.of(MediaType.ALL));
        httpHeaders.add("user-agent", cvlPancheckJsonClientConfig.getUserAgent());
        httpHeaders.add("api_key", cvlPancheckJsonClientConfig.getApiKey());
        httpHeaders.add("tokenvalidtime", StringUtils.isBlank(tokenValidTime) ? cvlPancheckJsonClientConfig.getTokenValidTime() : tokenValidTime);

        log.info("GetToken Request Headers: {}", httpHeaders);
        log.info("GetToken Request Body: {}", payload);

        // Payload Encryption
        byte[] iv = generateIV();
        String ivBase64 = Base64.getEncoder().encodeToString(iv);
        String data = objectMapper.writeValueAsString(payload);
        String encryptedData = encryptString(cvlPancheckJsonClientConfig.getAesKey(), data, iv);
        String encryptedRequestData = ivBase64 + ":" + encryptedData;

        log.info("GetToken Encrypted Request Body: {}", encryptedRequestData);

        // Sending Request
        HttpEntity<String> requestEntity = new HttpEntity<>(encryptedRequestData, httpHeaders);
        ResponseEntity<String> response = restTemplate.postForEntity(cvlPancheckJsonClientConfig.getBaseUrl() + route, requestEntity, String.class);
        String responseString = response.getBody();
        log.info("GetToken Encrypted Response: {}", responseString);

        // Decrypt Response
        String[] splittedStrings = responseString.split(":");
        String responseIv = splittedStrings[0];
        String encryptedText = splittedStrings[1];
        String decryptedResponseData = decryptString(cvlPancheckJsonClientConfig.getAesKey(), encryptedText, responseIv);
        log.info("GetToken Decrypted String Response: {}", decryptedResponseData);

        if (decryptedResponseData == null) {
            throw new Exception("Failed to decrypt response data");
        }

        JsonNode responseJson = objectMapper.readTree(decryptedResponseData);
        log.info("GetToken Decrypted Json Response: {}", responseJson);

        return responseJson;
    }


    // Service config
    @Data
    @Builder
    public static class CvlPancheckJsonClientConfig {
        private String baseUrl;
        private String userAgent;
        private String apiKey;
        private String aesKey;
        private String username;
        private String password;
        private String posCode;
        private String rtaCode;
        private String tokenValidTime;
    }

    // Service routes
    public static class CvlPancheckJsonClientRoutes {
        public static final String GET_TOKEN = "/int/api/GetToken";
        public static final String GET_PAN_STATUS = "/int/api/GetPanStatus";
        public static final String SOLICIT_PAN_DETAILS_FETCH_ALL_KRA = "/int/api/SolicitPANDetailsFetchALLKRA";
        public static final String INSERT_UPDATE_KYC_RECORD = "/int/api/InsertUpdateKYCRecord";
    }


    // Private utility methods
    private byte[] generateIV() {
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    private String encryptString(String aesKey, String data, byte[] iv) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(aesKey);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    private String decryptString(String aesKey, String encryptedText, String iv) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(aesKey);
            byte[] ivBytes = Base64.getDecoder().decode(iv);

            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            System.err.println("Decryption error: " + e.getMessage());
            return null;
        }
    }


}
