package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Additional PEP (Politically Exposed Person) status constants and enum
 */
public class AdditionalPepStatus {

    // Constants
    /**
     * Not applicable
     */
    public static final String NA = "NA";
    public static final String NOT_APPLICABLE = "NOT_APPLICABLE";
    public static final String NOT_APPLICABLE_CODE = "NA";
    public static final String NOT_APPLICABLE_DESC = "Not Applicable";

    /**
     * Politically exposed person
     */
    public static final String PEP = "PEP";
    public static final String POLITICALLY_EXPOSED_PERSON = "POLITICALLY_EXPOSED_PERSON";
    public static final String POLITICALLY_EXPOSED_PERSON_CODE = "PEP";
    public static final String POLITICALLY_EXPOSED_PERSON_DESC = "Politically Exposed Person";

    /**
     * Related to politically exposed person
     */
    public static final String RPEP = "RPEP";
    public static final String RELATED_TO_POLITICALLY_EXPOSED_PERSON = "RELATED_TO_POLITICALLY_EXPOSED_PERSON";
    public static final String RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE = "RPEP";
    public static final String RELATED_TO_POLITICALLY_EXPOSED_PERSON_DESC = "Related To Politically Exposed Person";


    /**
     * List of additional PEP status codes
     */
    public static final List<String> ADDITIONAL_PEP_STATUS_CODES = Arrays.asList(NOT_APPLICABLE_CODE, RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE, POLITICALLY_EXPOSED_PERSON_CODE);

    /**
     * List of additional PEP status descriptions
     */
    public static final List<String> ADDITIONAL_PEP_STATUS_DESCRIPTIONS = Arrays.asList(NOT_APPLICABLE_DESC, RELATED_TO_POLITICALLY_EXPOSED_PERSON_DESC, POLITICALLY_EXPOSED_PERSON_DESC);

    /**
     * List of additional PEP status codes as enum
     */
    public static final List<PepStatus> ADDITIONAL_PEP_STATUS_CODES_AS_ENUM = Arrays.asList(PepStatus.NOT_APPLICABLE, PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON, PepStatus.POLITICALLY_EXPOSED_PERSON);

    /**
     * PEP Status enum
     */
    public enum PepStatus {
        NOT_APPLICABLE(NOT_APPLICABLE_CODE, NOT_APPLICABLE_DESC), RELATED_TO_POLITICALLY_EXPOSED_PERSON(RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE, RELATED_TO_POLITICALLY_EXPOSED_PERSON_DESC), POLITICALLY_EXPOSED_PERSON(POLITICALLY_EXPOSED_PERSON_CODE, POLITICALLY_EXPOSED_PERSON_DESC);

        private final String description;
        private final String code;

        PepStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * Get the code representation of this status
         *
         * @return the code string
         */
        public String asCode() {
            return code;
//            return switch (this) {
//                case NOT_APPLICABLE -> NOT_APPLICABLE_CODE;
//                case RELATED_TO_POLITICALLY_EXPOSED_PERSON -> RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE;
//                case POLITICALLY_EXPOSED_PERSON -> POLITICALLY_EXPOSED_PERSON_CODE;
//                default -> throw new IllegalStateException("Unknown PEP status: " + this);
//            };
        }

        /**
         * Get the description of this status
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the PEP status from the code
         *
         * @param code the code string
         * @return Optional containing the status if found, empty otherwise
         */
        public static Optional<PepStatus> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            return switch (code) {
                case NOT_APPLICABLE_CODE -> Optional.of(NOT_APPLICABLE);
                case RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE -> Optional.of(RELATED_TO_POLITICALLY_EXPOSED_PERSON);
                case POLITICALLY_EXPOSED_PERSON_CODE -> Optional.of(POLITICALLY_EXPOSED_PERSON);
                default -> Optional.empty();
            };
        }

        /**
         * Parse PEP status from string description
         *
         * @param description the description string
         * @return the PEP status
         * @throws IllegalArgumentException if the description is invalid
         */
        public static PepStatus fromDescription(String description) {
            if (description == null) {
                throw new IllegalArgumentException("Description cannot be null");
            }

            switch (description) {
                case AdditionalPepStatus.NOT_APPLICABLE_DESC:
                    return PepStatus.NOT_APPLICABLE;
                case AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON_DESC:
                    return PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON;
                case AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON_DESC:
                    return PepStatus.POLITICALLY_EXPOSED_PERSON;
                default:
                    throw new IllegalArgumentException("Invalid PEP status: " + description);
            }
        }
    }
}