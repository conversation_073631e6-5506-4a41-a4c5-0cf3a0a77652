package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Additional Update Flag constants and enum
 */
public class AdditionalUpdateFlag {

    // Constants
    /**
     * New
     */
    public static final String NEW = "NEW";
    public static final String NEW_CODE = "01";
    public static final String NEW_DESC = "New";

    /**
     * Modify with documents
     */
    public static final String MODIFY_WITH_DOCUMENTS = "MODIFY_WITH_DOCUMENTS";
    public static final String MODIFY_WITH_DOCUMENTS_CODE = "02";
    public static final String MODIFY_WITH_DOCUMENTS_DESC = "Modify with documents";

    /**
     * Modify without documents
     */
    public static final String MODIFY_WITHOUT_DOCUMENTS = "MODIFY_WITHOUT_DOCUMENTS";
    public static final String MODIFY_WITHOUT_DOCUMENTS_CODE = "03";
    public static final String MODIFY_WITHOUT_DOCUMENTS_DESC = "Modify without documents";

    /**
     * Delete
     */
    public static final String DELETE = "DELETE";
    public static final String DELETE_CODE = "04";
    public static final String DELETE_DESC = "Delete";

    /**
     * Old KYC Record
     */
    public static final String OLD_KYC_RECORD = "OLD_KYC_RECORD";
    public static final String OLD_KYC_RECORD_CODE = "07";
    public static final String OLD_KYC_RECORD_DESC = "Old KYC Record";

    /**
     * Interop Modification
     */
    public static final String INTEROP_MODIFICATION = "INTEROP_MODIFICATION";
    public static final String INTEROP_MODIFICATION_CODE = "99";
    public static final String INTEROP_MODIFICATION_DESC = "Interop Modification";

    /**
     * List of all additional update flag codes
     */
    public static final List<String> ADDITIONAL_UPDATE_FLAG_CODES = Arrays.asList(
            NEW_CODE, MODIFY_WITH_DOCUMENTS_CODE, MODIFY_WITHOUT_DOCUMENTS_CODE,
            DELETE_CODE, OLD_KYC_RECORD_CODE, INTEROP_MODIFICATION_CODE);

    /**
     * List of additional update flag codes as enum
     */
    public static final List<AdditionalUpdateFlagType> ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM = Arrays.asList(
            AdditionalUpdateFlagType.NEW,
            AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS,
            AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS,
            AdditionalUpdateFlagType.DELETE,
            AdditionalUpdateFlagType.OLD_KYC_RECORD,
            AdditionalUpdateFlagType.INTEROP_MODIFICATION);

    /**
     * Additional Update Flag Type enum
     */
    public enum AdditionalUpdateFlagType {
        NEW("New"),
        MODIFY_WITH_DOCUMENTS("Modify with documents"),
        MODIFY_WITHOUT_DOCUMENTS("Modify without documents"),
        DELETE("Delete"),
        OLD_KYC_RECORD("Old KYC Record"),
        INTEROP_MODIFICATION("Interop Modification");

        private final String description;

        AdditionalUpdateFlagType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case NEW:
                    return NEW_CODE;
                case MODIFY_WITH_DOCUMENTS:
                    return MODIFY_WITH_DOCUMENTS_CODE;
                case MODIFY_WITHOUT_DOCUMENTS:
                    return MODIFY_WITHOUT_DOCUMENTS_CODE;
                case DELETE:
                    return DELETE_CODE;
                case OLD_KYC_RECORD:
                    return OLD_KYC_RECORD_CODE;
                case INTEROP_MODIFICATION:
                    return INTEROP_MODIFICATION_CODE;
                default:
                    throw new IllegalStateException("Unknown additional update flag type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the additional update flag type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<AdditionalUpdateFlagType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case NEW_CODE:
                    return Optional.of(AdditionalUpdateFlagType.NEW);
                case MODIFY_WITH_DOCUMENTS_CODE:
                    return Optional.of(AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS);
                case MODIFY_WITHOUT_DOCUMENTS_CODE:
                    return Optional.of(AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS);
                case DELETE_CODE:
                    return Optional.of(AdditionalUpdateFlagType.DELETE);
                case OLD_KYC_RECORD_CODE:
                    return Optional.of(AdditionalUpdateFlagType.OLD_KYC_RECORD);
                case INTEROP_MODIFICATION_CODE:
                    return Optional.of(AdditionalUpdateFlagType.INTEROP_MODIFICATION);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse additional update flag type from string
         *
         * @param value the string value
         * @return the additional update flag type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static AdditionalUpdateFlagType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case AdditionalUpdateFlag.NEW:
                    return AdditionalUpdateFlagType.NEW;
                case AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS:
                    return AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS;
                case AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS:
                    return AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS;
                case AdditionalUpdateFlag.DELETE:
                    return AdditionalUpdateFlagType.DELETE;
                case AdditionalUpdateFlag.OLD_KYC_RECORD:
                    return AdditionalUpdateFlagType.OLD_KYC_RECORD;
                case AdditionalUpdateFlag.INTEROP_MODIFICATION:
                    return AdditionalUpdateFlagType.INTEROP_MODIFICATION;
                default:
                    throw new IllegalArgumentException("Invalid additional update flag: " + value);
            }
        }
    }
}
