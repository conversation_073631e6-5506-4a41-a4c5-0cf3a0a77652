package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Address Proof constants and enum
 */
public class AddressProof {

    // Constants
    /**
     * Passport
     */
    public static final String PASSPORT = "PASSPORT";
    public static final String PASSPORT_CODE = "01";

    /**
     * Driving License
     */
    public static final String DRIVING_LICENSE = "DRIVING_LICENSE";
    public static final String DRIVING_LICENSE_CODE = "02";

    /**
     * Latest Bank Passbook
     */
    public static final String BANK_PASSBOOK = "BANK_PASSBOOK";
    public static final String BANK_PASSBOOK_CODE = "03";

    /**
     * Latest Bank Account Statement
     */
    public static final String BANK_STATEMENT = "BANK_STATEMENT";
    public static final String BANK_STATEMENT_CODE = "04";

    /**
     * Voter Identity Card
     */
    public static final String VOTER_ID_CARD = "VOTER_ID_CARD";
    public static final String VOTER_ID_CARD_CODE = "06";

    /**
     * Ration Card
     */
    public static final String RATION_CARD = "RATION_CARD";
    public static final String RATION_CARD_CODE = "07";

    /**
     * Registered Lease / Sale Agreement of Residence
     */
    public static final String LEASE_SALE_AGREEMENT = "LEASE_SALE_AGREEMENT";
    public static final String LEASE_SALE_AGREEMENT_CODE = "08";

    /**
     * Latest Land Line Telephone Bill
     */
    public static final String TELEPHONE_BILL = "TELEPHONE_BILL";
    public static final String TELEPHONE_BILL_CODE = "09";

    /**
     * Latest Electricity Bill
     */
    public static final String ELECTRICITY_BILL = "ELECTRICITY_BILL";
    public static final String ELECTRICITY_BILL_CODE = "10";

    /**
     * Gas Bill
     */
    public static final String GAS_BILL = "GAS_BILL";
    public static final String GAS_BILL_CODE = "11";

    /**
     * Flat Maintenance Bill
     */
    public static final String FLAT_MAINTENANCE_BILL = "FLAT_MAINTENANCE_BILL";
    public static final String FLAT_MAINTENANCE_BILL_CODE = "12";

    /**
     * Insurance copy
     */
    public static final String INSURANCE_COPY = "INSURANCE_COPY";
    public static final String INSURANCE_COPY_CODE = "13";

    /**
     * Self Declaration by High Court / Supreme Court Judge
     */
    public static final String HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY = "HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY";
    public static final String HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY_CODE = "14";

    /**
     * Proof of Address issued by Scheduled Commercial Banks / Scheduled Co-operative Banks / Multinational Foreign banks
     */
    public static final String SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS = "SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS";
    public static final String SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS_CODE = "15";

    /**
     * Proof of Address issued by Elected representatives to the Legislative Assembly
     */
    public static final String ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY = "ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY";
    public static final String ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY_CODE = "16";

    /**
     * Proof of Address issued by Parliament
     */
    public static final String PARLIAMENT = "PARLIAMENT";
    public static final String PARLIAMENT_CODE = "17";

    /**
     * Proof of Address issued by any Government / Statutory Authority
     */
    public static final String GOVERNMENT_STATUTORY_AUTHORITY = "GOVERNMENT_STATUTORY_AUTHORITY";
    public static final String GOVERNMENT_STATUTORY_AUTHORITY_CODE = "18";

    /**
     * Proof of Address issued by Notary Public
     */
    public static final String NOTARY_PUBLIC = "NOTARY_PUBLIC";
    public static final String NOTARY_PUBLIC_CODE = "19";

    /**
     * Proof of Address issued by Gazetted Officer
     */
    public static final String GAZETTED_OFFICER = "GAZETTED_OFFICER";
    public static final String GAZETTED_OFFICER_CODE = "20";

    /**
     * ID Card with address issued by Central / State Government
     */
    public static final String CENTRAL_STATE_GOVERNMENT_ID_CARD = "CENTRAL_STATE_GOVERNMENT_ID_CARD";
    public static final String CENTRAL_STATE_GOVERNMENT_ID_CARD_CODE = "21";

    /**
     * ID Card with address issued by Statutory / Regulatory Authorities
     */
    public static final String STATUTORY_REGULATORY_AUTHORITIES_ID_CARD = "STATUTORY_REGULATORY_AUTHORITIES_ID_CARD";
    public static final String STATUTORY_REGULATORY_AUTHORITIES_ID_CARD_CODE = "22";

    /**
     * ID Card with address issued by Public Sector Undertakings
     */
    public static final String PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD = "PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD";
    public static final String PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD_CODE = "23";

    /**
     * ID Card with address issued by Scheduled Commercial Banks
     */
    public static final String SCHEDULED_COMMERCIAL_BANKS_ID_CARD = "SCHEDULED_COMMERCIAL_BANKS_ID_CARD";
    public static final String SCHEDULED_COMMERCIAL_BANKS_ID_CARD_CODE = "24";

    /**
     * ID Card with address issued by Public Financial Institutions
     */
    public static final String PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD = "PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD";
    public static final String PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD_CODE = "25";

    /**
     * ID Card with address issued by Colleges affiliated to Universities
     */
    public static final String COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD = "COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD";
    public static final String COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD_CODE = "26";

    /**
     * ID Card issued by Professional Bodies
     */
    public static final String PROFESSIONAL_BODIES_ID_CARD = "PROFESSIONAL_BODIES_ID_CARD";
    public static final String PROFESSIONAL_BODIES_ID_CARD_CODE = "29";

    /**
     * AADHAAR
     */
    public static final String AADHAAR = "AADHAAR";
    public static final String AADHAAR_CODE = "31";

    /**
     * ANY OTHER PROOF OF ADDRESS
     */
    public static final String OTHER = "OTHER";
    public static final String OTHER_CODE = "32";

    /**
     * NREGA JOB CARD
     */
    public static final String NAREGA_JOB_CARD = "NAREGA_JOB_CARD";
    public static final String NAREGA_JOB_CARD_CODE = "33";

    /**
     * PENSION OR FAMILY PENSION PAYMENT ORDERS
     */
    public static final String PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS = "PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS";
    public static final String PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS_CODE = "34";

    /**
     * SARAL KYC SELF DECLARATION
     */
    public static final String SARAL_KYC_SELF_DECLARATION = "SARAL_KYC_SELF_DECLARATION";
    public static final String SARAL_KYC_SELF_DECLARATION_CODE = "35";

    /**
     * List of all address proof codes
     */
    public static final List<String> ADDRESS_PROOF_CODES = Arrays.asList(
            PASSPORT_CODE, DRIVING_LICENSE_CODE, BANK_PASSBOOK_CODE, BANK_STATEMENT_CODE,
            VOTER_ID_CARD_CODE, RATION_CARD_CODE, LEASE_SALE_AGREEMENT_CODE, TELEPHONE_BILL_CODE,
            ELECTRICITY_BILL_CODE, GAS_BILL_CODE, FLAT_MAINTENANCE_BILL_CODE, INSURANCE_COPY_CODE,
            HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY_CODE,
            SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS_CODE,
            ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY_CODE, PARLIAMENT_CODE,
            GOVERNMENT_STATUTORY_AUTHORITY_CODE, NOTARY_PUBLIC_CODE, GAZETTED_OFFICER_CODE,
            CENTRAL_STATE_GOVERNMENT_ID_CARD_CODE, STATUTORY_REGULATORY_AUTHORITIES_ID_CARD_CODE,
            PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD_CODE, SCHEDULED_COMMERCIAL_BANKS_ID_CARD_CODE,
            PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD_CODE, COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD_CODE,
            PROFESSIONAL_BODIES_ID_CARD_CODE, AADHAAR_CODE, OTHER_CODE, NAREGA_JOB_CARD_CODE,
            PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS_CODE, SARAL_KYC_SELF_DECLARATION_CODE);

    /**
     * Address Proof Type enum
     */
    public enum AddressProofType {
        PASSPORT("Passport"),
        DRIVING_LICENSE("Driving License"),
        BANK_PASSBOOK("Latest Bank Passbook"),
        BANK_STATEMENT("Latest Bank Account Statement"),
        VOTER_ID_CARD("Voter Identity Card"),
        RATION_CARD("Ration Card"),
        LEASE_SALE_AGREEMENT("Registered Lease / Sale Agreement of Residence"),
        TELEPHONE_BILL("Latest Land Line Telephone Bill"),
        ELECTRICITY_BILL("Latest Electricity Bill"),
        GAS_BILL("Gas Bill"),
        FLAT_MAINTENANCE_BILL("Flat Maintenance Bill"),
        INSURANCE_COPY("Insurance copy"),
        HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY("Self Declaration by High Court / Supreme Court Judge"),
        SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS("Proof of Address issued by Scheduled Commercial Banks / Scheduled Co-operative Banks / Multinational Foreign banks"),
        ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY("Proof of Address issued by Elected representatives to the Legislative Assembly"),
        PARLIAMENT("Proof of Address issued by Parliament"),
        GOVERNMENT_STATUTORY_AUTHORITY("Proof of Address issued by any Government / Statutory Authority"),
        NOTARY_PUBLIC("Proof of Address issued by Notary Public"),
        GAZETTED_OFFICER("Proof of Address issued by Gazetted Officer"),
        CENTRAL_STATE_GOVERNMENT_ID_CARD("ID Card with address issued by Central / State Government"),
        STATUTORY_REGULATORY_AUTHORITIES_ID_CARD("ID Card with address issued by Statutory / Regulatory Authorities"),
        PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD("ID Card with address issued by Public Sector Undertakings"),
        SCHEDULED_COMMERCIAL_BANKS_ID_CARD("ID Card with address issued by Scheduled Commercial Banks"),
        PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD("ID Card with address issued by Public Financial Institutions"),
        COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD("ID Card with address issued by Colleges affiliated to Universities"),
        PROFESSIONAL_BODIES_ID_CARD("ID Card issued by Professional Bodies"),
        AADHAAR("AADHAAR"),
        OTHER("ANY OTHER PROOF OF ADDRESS"),
        NAREGA_JOB_CARD("NREGA JOB CARD"),
        PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS("PENSION OR FAMILY PENSION PAYMENT ORDERS"),
        SARAL_KYC_SELF_DECLARATION("SARAL KYC SELF DECLARATION");

        private final String description;

        AddressProofType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case PASSPORT:
                    return PASSPORT_CODE;
                case DRIVING_LICENSE:
                    return DRIVING_LICENSE_CODE;
                case BANK_PASSBOOK:
                    return BANK_PASSBOOK_CODE;
                case BANK_STATEMENT:
                    return BANK_STATEMENT_CODE;
                case VOTER_ID_CARD:
                    return VOTER_ID_CARD_CODE;
                case RATION_CARD:
                    return RATION_CARD_CODE;
                case LEASE_SALE_AGREEMENT:
                    return LEASE_SALE_AGREEMENT_CODE;
                case TELEPHONE_BILL:
                    return TELEPHONE_BILL_CODE;
                case ELECTRICITY_BILL:
                    return ELECTRICITY_BILL_CODE;
                case GAS_BILL:
                    return GAS_BILL_CODE;
                case FLAT_MAINTENANCE_BILL:
                    return FLAT_MAINTENANCE_BILL_CODE;
                case INSURANCE_COPY:
                    return INSURANCE_COPY_CODE;
                case HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY:
                    return HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY_CODE;
                case SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS:
                    return SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS_CODE;
                case ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY:
                    return ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY_CODE;
                case PARLIAMENT:
                    return PARLIAMENT_CODE;
                case GOVERNMENT_STATUTORY_AUTHORITY:
                    return GOVERNMENT_STATUTORY_AUTHORITY_CODE;
                case NOTARY_PUBLIC:
                    return NOTARY_PUBLIC_CODE;
                case GAZETTED_OFFICER:
                    return GAZETTED_OFFICER_CODE;
                case CENTRAL_STATE_GOVERNMENT_ID_CARD:
                    return CENTRAL_STATE_GOVERNMENT_ID_CARD_CODE;
                case STATUTORY_REGULATORY_AUTHORITIES_ID_CARD:
                    return STATUTORY_REGULATORY_AUTHORITIES_ID_CARD_CODE;
                case PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD:
                    return PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD_CODE;
                case SCHEDULED_COMMERCIAL_BANKS_ID_CARD:
                    return SCHEDULED_COMMERCIAL_BANKS_ID_CARD_CODE;
                case PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD:
                    return PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD_CODE;
                case COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD:
                    return COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD_CODE;
                case PROFESSIONAL_BODIES_ID_CARD:
                    return PROFESSIONAL_BODIES_ID_CARD_CODE;
                case AADHAAR:
                    return AADHAAR_CODE;
                case OTHER:
                    return OTHER_CODE;
                case NAREGA_JOB_CARD:
                    return NAREGA_JOB_CARD_CODE;
                case PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS:
                    return PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS_CODE;
                case SARAL_KYC_SELF_DECLARATION:
                    return SARAL_KYC_SELF_DECLARATION_CODE;
                default:
                    throw new IllegalStateException("Unknown address proof type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the address proof type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<AddressProofType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case PASSPORT_CODE:
                    return Optional.of(AddressProofType.PASSPORT);
                case DRIVING_LICENSE_CODE:
                    return Optional.of(AddressProofType.DRIVING_LICENSE);
                case BANK_PASSBOOK_CODE:
                    return Optional.of(AddressProofType.BANK_PASSBOOK);
                case BANK_STATEMENT_CODE:
                    return Optional.of(AddressProofType.BANK_STATEMENT);
                case VOTER_ID_CARD_CODE:
                    return Optional.of(AddressProofType.VOTER_ID_CARD);
                case RATION_CARD_CODE:
                    return Optional.of(AddressProofType.RATION_CARD);
                case LEASE_SALE_AGREEMENT_CODE:
                    return Optional.of(AddressProofType.LEASE_SALE_AGREEMENT);
                case TELEPHONE_BILL_CODE:
                    return Optional.of(AddressProofType.TELEPHONE_BILL);
                case ELECTRICITY_BILL_CODE:
                    return Optional.of(AddressProofType.ELECTRICITY_BILL);
                case GAS_BILL_CODE:
                    return Optional.of(AddressProofType.GAS_BILL);
                case FLAT_MAINTENANCE_BILL_CODE:
                    return Optional.of(AddressProofType.FLAT_MAINTENANCE_BILL);
                case INSURANCE_COPY_CODE:
                    return Optional.of(AddressProofType.INSURANCE_COPY);
                case HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY_CODE:
                    return Optional.of(AddressProofType.HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY);
                case SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS_CODE:
                    return Optional.of(AddressProofType.SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS);
                case ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY_CODE:
                    return Optional.of(AddressProofType.ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY);
                case PARLIAMENT_CODE:
                    return Optional.of(AddressProofType.PARLIAMENT);
                case GOVERNMENT_STATUTORY_AUTHORITY_CODE:
                    return Optional.of(AddressProofType.GOVERNMENT_STATUTORY_AUTHORITY);
                case NOTARY_PUBLIC_CODE:
                    return Optional.of(AddressProofType.NOTARY_PUBLIC);
                case GAZETTED_OFFICER_CODE:
                    return Optional.of(AddressProofType.GAZETTED_OFFICER);
                case CENTRAL_STATE_GOVERNMENT_ID_CARD_CODE:
                    return Optional.of(AddressProofType.CENTRAL_STATE_GOVERNMENT_ID_CARD);
                case STATUTORY_REGULATORY_AUTHORITIES_ID_CARD_CODE:
                    return Optional.of(AddressProofType.STATUTORY_REGULATORY_AUTHORITIES_ID_CARD);
                case PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD_CODE:
                    return Optional.of(AddressProofType.PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD);
                case SCHEDULED_COMMERCIAL_BANKS_ID_CARD_CODE:
                    return Optional.of(AddressProofType.SCHEDULED_COMMERCIAL_BANKS_ID_CARD);
                case PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD_CODE:
                    return Optional.of(AddressProofType.PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD);
                case COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD_CODE:
                    return Optional.of(AddressProofType.COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD);
                case PROFESSIONAL_BODIES_ID_CARD_CODE:
                    return Optional.of(AddressProofType.PROFESSIONAL_BODIES_ID_CARD);
                case AADHAAR_CODE:
                    return Optional.of(AddressProofType.AADHAAR);
                case OTHER_CODE:
                    return Optional.of(AddressProofType.OTHER);
                case NAREGA_JOB_CARD_CODE:
                    return Optional.of(AddressProofType.NAREGA_JOB_CARD);
                case PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS_CODE:
                    return Optional.of(AddressProofType.PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS);
                case SARAL_KYC_SELF_DECLARATION_CODE:
                    return Optional.of(AddressProofType.SARAL_KYC_SELF_DECLARATION);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse address proof type from string
         *
         * @param value the string value
         * @return the address proof type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static AddressProofType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case AddressProof.PASSPORT:
                    return AddressProofType.PASSPORT;
                case AddressProof.DRIVING_LICENSE:
                    return AddressProofType.DRIVING_LICENSE;
                case AddressProof.BANK_PASSBOOK:
                    return AddressProofType.BANK_PASSBOOK;
                case AddressProof.BANK_STATEMENT:
                    return AddressProofType.BANK_STATEMENT;
                case AddressProof.VOTER_ID_CARD:
                    return AddressProofType.VOTER_ID_CARD;
                case AddressProof.RATION_CARD:
                    return AddressProofType.RATION_CARD;
                case AddressProof.LEASE_SALE_AGREEMENT:
                    return AddressProofType.LEASE_SALE_AGREEMENT;
                case AddressProof.TELEPHONE_BILL:
                    return AddressProofType.TELEPHONE_BILL;
                case AddressProof.ELECTRICITY_BILL:
                    return AddressProofType.ELECTRICITY_BILL;
                case AddressProof.GAS_BILL:
                    return AddressProofType.GAS_BILL;
                case AddressProof.FLAT_MAINTENANCE_BILL:
                    return AddressProofType.FLAT_MAINTENANCE_BILL;
                case AddressProof.INSURANCE_COPY:
                    return AddressProofType.INSURANCE_COPY;
                case AddressProof.HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY:
                    return AddressProofType.HIGH_COURT_SUPREME_COURT_JUDGE_POWER_OF_ATTORNEY;
                case AddressProof.SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS:
                    return AddressProofType.SCHEDULED_COMMERCIAL_BANKS_SCHEDULED_CO_OPERATIVE_BANKS_MULTINATIONAL_FOREIGN_BANKS;
                case AddressProof.ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY:
                    return AddressProofType.ELECTED_REPRESENTATIVES_TO_LEGISLATIVE_ASSEMBLY;
                case AddressProof.PARLIAMENT:
                    return AddressProofType.PARLIAMENT;
                case AddressProof.GOVERNMENT_STATUTORY_AUTHORITY:
                    return AddressProofType.GOVERNMENT_STATUTORY_AUTHORITY;
                case AddressProof.NOTARY_PUBLIC:
                    return AddressProofType.NOTARY_PUBLIC;
                case AddressProof.GAZETTED_OFFICER:
                    return AddressProofType.GAZETTED_OFFICER;
                case AddressProof.CENTRAL_STATE_GOVERNMENT_ID_CARD:
                    return AddressProofType.CENTRAL_STATE_GOVERNMENT_ID_CARD;
                case AddressProof.STATUTORY_REGULATORY_AUTHORITIES_ID_CARD:
                    return AddressProofType.STATUTORY_REGULATORY_AUTHORITIES_ID_CARD;
                case AddressProof.PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD:
                    return AddressProofType.PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD;
                case AddressProof.SCHEDULED_COMMERCIAL_BANKS_ID_CARD:
                    return AddressProofType.SCHEDULED_COMMERCIAL_BANKS_ID_CARD;
                case AddressProof.PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD:
                    return AddressProofType.PUBLIC_FINANCIAL_INSTITUTIONS_ID_CARD;
                case AddressProof.COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD:
                    return AddressProofType.COLLEGES_AFFILIATED_TO_UNIVERSITIES_ID_CARD;
                case AddressProof.PROFESSIONAL_BODIES_ID_CARD:
                    return AddressProofType.PROFESSIONAL_BODIES_ID_CARD;
                case AddressProof.AADHAAR:
                    return AddressProofType.AADHAAR;
                case AddressProof.OTHER:
                    return AddressProofType.OTHER;
                case AddressProof.NAREGA_JOB_CARD:
                    return AddressProofType.NAREGA_JOB_CARD;
                case AddressProof.PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS:
                    return AddressProofType.PENSION_OR_FAMILY_PENSION_PAYMENT_ORDERS;
                case AddressProof.SARAL_KYC_SELF_DECLARATION:
                    return AddressProofType.SARAL_KYC_SELF_DECLARATION;
                default:
                    throw new IllegalArgumentException("Invalid address proof: " + value);
            }
        }
    }
}
