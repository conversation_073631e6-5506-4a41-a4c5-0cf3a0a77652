package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Company Type constants and enum
 */
public class CompanyType {

    // Constants
    /**
     * Private Limited Company
     */
    public static final String PRIVATE_LTD_COMPANY = "PRIVATE_LTD_COMPANY";
    public static final String PRIVATE_LTD_COMPANY_CODE = "01";
    public static final String PRIVATE_LTD_COMPANY_DESC = "Private Limited Company";

    /**
     * Public Limited Company
     */
    public static final String PUBLIC_LTD_COMPANY = "PUBLIC_LTD_COMPANY";
    public static final String PUBLIC_LTD_COMPANY_CODE = "02";
    public static final String PUBLIC_LTD_COMPANY_DESC = "Public Limited Company";

    /**
     * Body Corporate
     */
    public static final String BODY_CORPORATE = "BODY_CORPORATE";
    public static final String BODY_CORPORATE_CODE = "03";
    public static final String BODY_CORPORATE_DESC = "Body Corporate";

    /**
     * Partnership
     */
    public static final String PARTNERSHIP = "PARTNERSHIP";
    public static final String PARTNERSHIP_CODE = "04";
    public static final String PARTNERSHIP_DESC = "Partnership";

    /**
     * Trust Charities NGOs
     */
    public static final String TRUST_CHARITIES_NGOS = "TRUST_CHARITIES_NGOs";
    public static final String TRUST_CHARITIES_NGOS_CODE = "05";
    public static final String TRUST_CHARITIES_NGOS_DESC = "Trust Charities NGOs";

    /**
     * FI
     */
    public static final String FI = "FI";
    public static final String FI_CODE = "06";
    public static final String FI_DESC = "FI";

    /**
     * FII
     */
    public static final String FII = "FII";
    public static final String FII_CODE = "07";
    public static final String FII_DESC = "FII";

    /**
     * HUF
     */
    public static final String HUF = "HUF";
    public static final String HUF_CODE = "08";
    public static final String HUF_DESC = "HUF";

    /**
     * AOP
     */
    public static final String AOP = "AOP";
    public static final String AOP_CODE = "09";
    public static final String AOP_DESC = "AOP";

    /**
     * Bank
     */
    public static final String BANK = "BANK";
    public static final String BANK_CODE = "10";
    public static final String BANK_DESC = "Bank";

    /**
     * Government Body
     */
    public static final String GOVERNMENT_BODY = "GOVERNMENT_BODY";
    public static final String GOVERNMENT_BODY_CODE = "11";
    public static final String GOVERNMENT_BODY_DESC = "Government Body";

    /**
     * Non Government Organisation
     */
    public static final String NON_GOVERNMENT_ORGANISATION = "NON_GOVERNMENT_ORGANISATION";
    public static final String NON_GOVERNMENT_ORGANISATION_CODE = "12";
    public static final String NON_GOVERNMENT_ORGANISATION_DESC = "Non Government Organisation";

    /**
     * Defense Establishment
     */
    public static final String DEFENSE_ESTABLISHMENT = "DEFENSE_ESTABLISHMENT";
    public static final String DEFENSE_ESTABLISHMENT_CODE = "13";
    public static final String DEFENSE_ESTABLISHMENT_DESC = "Defense Establishment";

    /**
     * Body of Individuals
     */
    public static final String BODY_OF_INDIVIDUALS = "BODY_OF_INDIVIDUALS";
    public static final String BODY_OF_INDIVIDUALS_CODE = "14";
    public static final String BODY_OF_INDIVIDUALS_DESC = "Body of Individuals";

    /**
     * Society
     */
    public static final String SOCIETY = "SOCIETY";
    public static final String SOCIETY_CODE = "15";
    public static final String SOCIETY_DESC = "Society";

    /**
     * LLP
     */
    public static final String LLP = "LLP";
    public static final String LLP_CODE = "16";
    public static final String LLP_DESC = "LLP";

    /**
     * QFI
     */
    public static final String QFI = "QFI";
    public static final String QFI_CODE = "17";
    public static final String QFI_DESC = "QFI";

    /**
     * Eligible Foreign Investor Category I
     */
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I = "ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I_CODE = "18";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I_DESC = "Eligible Foreign Investor Category I";

    /**
     * Eligible Foreign Investor Category II
     */
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II = "ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II_CODE = "19";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II_DESC = "Eligible Foreign Investor Category II";

    /**
     * Eligible Foreign Investor Category III
     */
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III = "ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE = "20";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_DESC = "Eligible Foreign Investor Category III";

    /**
     * Others
     */
    public static final String OTHERS = "OTHERS";
    public static final String OTHERS_CODE = "99";
    public static final String OTHERS_DESC = "Others";

    /**
     * List of all company type codes
     */
    public static final List<String> COMPANY_TYPE_CODES = Arrays.asList(
            PRIVATE_LTD_COMPANY_CODE, PUBLIC_LTD_COMPANY_CODE, BODY_CORPORATE_CODE, PARTNERSHIP_CODE,
            TRUST_CHARITIES_NGOS_CODE, FI_CODE, FII_CODE, HUF_CODE, AOP_CODE, BANK_CODE,
            GOVERNMENT_BODY_CODE, NON_GOVERNMENT_ORGANISATION_CODE, DEFENSE_ESTABLISHMENT_CODE,
            BODY_OF_INDIVIDUALS_CODE, SOCIETY_CODE, LLP_CODE, QFI_CODE,
            ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I_CODE, ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II_CODE,
            ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE, OTHERS_CODE);

    /**
     * List of company type codes as enum
     */
    public static final List<CompanyTypeType> COMPANY_TYPE_CODES_AS_ENUM = Arrays.asList(
            CompanyTypeType.PRIVATE_LTD_COMPANY,
            CompanyTypeType.PUBLIC_LTD_COMPANY,
            CompanyTypeType.BODY_CORPORATE,
            CompanyTypeType.PARTNERSHIP,
            CompanyTypeType.TRUST_CHARITIES_NGOS,
            CompanyTypeType.FI,
            CompanyTypeType.FII,
            CompanyTypeType.HUF,
            CompanyTypeType.AOP,
            CompanyTypeType.BANK,
            CompanyTypeType.GOVERNMENT_BODY,
            CompanyTypeType.NON_GOVERNMENT_ORGANISATION,
            CompanyTypeType.DEFENSE_ESTABLISHMENT,
            CompanyTypeType.BODY_OF_INDIVIDUALS,
            CompanyTypeType.SOCIETY,
            CompanyTypeType.LLP,
            CompanyTypeType.QFI,
            CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I,
            CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II,
            CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III,
            CompanyTypeType.OTHERS);

    /**
     * Company Type enum
     */
    public enum CompanyTypeType {
        PRIVATE_LTD_COMPANY("Private Limited Company"),
        PUBLIC_LTD_COMPANY("Public Limited Company"),
        BODY_CORPORATE("Body Corporate"),
        PARTNERSHIP("Partnership"),
        TRUST_CHARITIES_NGOS("Trust Charities NGOs"),
        FI("FI"),
        FII("FII"),
        HUF("HUF"),
        AOP("AOP"),
        BANK("Bank"),
        GOVERNMENT_BODY("Government Body"),
        NON_GOVERNMENT_ORGANISATION("Non Government Organisation"),
        DEFENSE_ESTABLISHMENT("Defense Establishment"),
        BODY_OF_INDIVIDUALS("Body of Individuals"),
        SOCIETY("Society"),
        LLP("LLP"),
        QFI("QFI"),
        ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I("Eligible Foreign Investor Category I"),
        ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II("Eligible Foreign Investor Category II"),
        ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III("Eligible Foreign Investor Category III"),
        OTHERS("Others");

        private final String description;

        CompanyTypeType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case PRIVATE_LTD_COMPANY:
                    return PRIVATE_LTD_COMPANY_CODE;
                case PUBLIC_LTD_COMPANY:
                    return PUBLIC_LTD_COMPANY_CODE;
                case BODY_CORPORATE:
                    return BODY_CORPORATE_CODE;
                case PARTNERSHIP:
                    return PARTNERSHIP_CODE;
                case TRUST_CHARITIES_NGOS:
                    return TRUST_CHARITIES_NGOS_CODE;
                case FI:
                    return FI_CODE;
                case FII:
                    return FII_CODE;
                case HUF:
                    return HUF_CODE;
                case AOP:
                    return AOP_CODE;
                case BANK:
                    return BANK_CODE;
                case GOVERNMENT_BODY:
                    return GOVERNMENT_BODY_CODE;
                case NON_GOVERNMENT_ORGANISATION:
                    return NON_GOVERNMENT_ORGANISATION_CODE;
                case DEFENSE_ESTABLISHMENT:
                    return DEFENSE_ESTABLISHMENT_CODE;
                case BODY_OF_INDIVIDUALS:
                    return BODY_OF_INDIVIDUALS_CODE;
                case SOCIETY:
                    return SOCIETY_CODE;
                case LLP:
                    return LLP_CODE;
                case QFI:
                    return QFI_CODE;
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I:
                    return ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I_CODE;
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II:
                    return ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II_CODE;
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III:
                    return ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE;
                case OTHERS:
                    return OTHERS_CODE;
                default:
                    throw new IllegalStateException("Unknown company type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the company type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<CompanyTypeType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case PRIVATE_LTD_COMPANY_CODE:
                    return Optional.of(CompanyTypeType.PRIVATE_LTD_COMPANY);
                case PUBLIC_LTD_COMPANY_CODE:
                    return Optional.of(CompanyTypeType.PUBLIC_LTD_COMPANY);
                case BODY_CORPORATE_CODE:
                    return Optional.of(CompanyTypeType.BODY_CORPORATE);
                case PARTNERSHIP_CODE:
                    return Optional.of(CompanyTypeType.PARTNERSHIP);
                case TRUST_CHARITIES_NGOS_CODE:
                    return Optional.of(CompanyTypeType.TRUST_CHARITIES_NGOS);
                case FI_CODE:
                    return Optional.of(CompanyTypeType.FI);
                case FII_CODE:
                    return Optional.of(CompanyTypeType.FII);
                case HUF_CODE:
                    return Optional.of(CompanyTypeType.HUF);
                case AOP_CODE:
                    return Optional.of(CompanyTypeType.AOP);
                case BANK_CODE:
                    return Optional.of(CompanyTypeType.BANK);
                case GOVERNMENT_BODY_CODE:
                    return Optional.of(CompanyTypeType.GOVERNMENT_BODY);
                case NON_GOVERNMENT_ORGANISATION_CODE:
                    return Optional.of(CompanyTypeType.NON_GOVERNMENT_ORGANISATION);
                case DEFENSE_ESTABLISHMENT_CODE:
                    return Optional.of(CompanyTypeType.DEFENSE_ESTABLISHMENT);
                case BODY_OF_INDIVIDUALS_CODE:
                    return Optional.of(CompanyTypeType.BODY_OF_INDIVIDUALS);
                case SOCIETY_CODE:
                    return Optional.of(CompanyTypeType.SOCIETY);
                case LLP_CODE:
                    return Optional.of(CompanyTypeType.LLP);
                case QFI_CODE:
                    return Optional.of(CompanyTypeType.QFI);
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I_CODE:
                    return Optional.of(CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I);
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II_CODE:
                    return Optional.of(CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II);
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE:
                    return Optional.of(CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III);
                case OTHERS_CODE:
                    return Optional.of(CompanyTypeType.OTHERS);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse company type from string
         *
         * @param value the string value
         * @return the company type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static CompanyTypeType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case CompanyType.PRIVATE_LTD_COMPANY:
                    return CompanyTypeType.PRIVATE_LTD_COMPANY;
                case CompanyType.PUBLIC_LTD_COMPANY:
                    return CompanyTypeType.PUBLIC_LTD_COMPANY;
                case CompanyType.BODY_CORPORATE:
                    return CompanyTypeType.BODY_CORPORATE;
                case CompanyType.PARTNERSHIP:
                    return CompanyTypeType.PARTNERSHIP;
                case CompanyType.TRUST_CHARITIES_NGOS:
                    return CompanyTypeType.TRUST_CHARITIES_NGOS;
                case CompanyType.FI:
                    return CompanyTypeType.FI;
                case CompanyType.FII:
                    return CompanyTypeType.FII;
                case CompanyType.HUF:
                    return CompanyTypeType.HUF;
                case CompanyType.AOP:
                    return CompanyTypeType.AOP;
                case CompanyType.BANK:
                    return CompanyTypeType.BANK;
                case CompanyType.GOVERNMENT_BODY:
                    return CompanyTypeType.GOVERNMENT_BODY;
                case CompanyType.NON_GOVERNMENT_ORGANISATION:
                    return CompanyTypeType.NON_GOVERNMENT_ORGANISATION;
                case CompanyType.DEFENSE_ESTABLISHMENT:
                    return CompanyTypeType.DEFENSE_ESTABLISHMENT;
                case CompanyType.BODY_OF_INDIVIDUALS:
                    return CompanyTypeType.BODY_OF_INDIVIDUALS;
                case CompanyType.SOCIETY:
                    return CompanyTypeType.SOCIETY;
                case CompanyType.LLP:
                    return CompanyTypeType.LLP;
                case CompanyType.QFI:
                    return CompanyTypeType.QFI;
                case CompanyType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I:
                    return CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I;
                case CompanyType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II:
                    return CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II;
                case CompanyType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III:
                    return CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III;
                case CompanyType.OTHERS:
                    return CompanyTypeType.OTHERS;
                default:
                    throw new IllegalArgumentException("Invalid company type: " + value);
            }
        }
    }
}
