package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Country Master constants and enum
 */
public class CountryMaster {

    // Constants
    /**
     * India
     */
    public static final String INDIA = "INDIA";
    public static final String INDIA_CODE = "101";
    public static final String INDIA_DESC = "India";

    /**
     * Albania
     */
    public static final String ALBANIA = "ALBANIA";
    public static final String ALBANIA_CODE = "003";
    public static final String ALBANIA_DESC = "Albania";

    /**
     * List of all country codes
     */
    public static final List<String> COUNTRY_CODES = Arrays.asList(INDIA_CODE, ALBANIA_CODE);

    /**
     * List of country codes as enum
     */
    public static final List<CountryMasterType> COUNTRY_CODES_AS_ENUM = Arrays.asList(
            CountryMasterType.INDIA,
            CountryMasterType.ALBANIA);

    /**
     * Country Master Type enum
     */
    public enum CountryMasterType {
        INDIA("India"),
        ALBANIA("Albania");

        private final String description;

        CountryMasterType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case INDIA:
                    return INDIA_CODE;
                case ALBANIA:
                    return ALBANIA_CODE;
                default:
                    throw new IllegalStateException("Unknown country type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the country type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<CountryMasterType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case INDIA_CODE:
                    return Optional.of(CountryMasterType.INDIA);
                case ALBANIA_CODE:
                    return Optional.of(CountryMasterType.ALBANIA);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse country type from string
         *
         * @param value the string value
         * @return the country type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static CountryMasterType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case CountryMaster.INDIA:
                    return CountryMasterType.INDIA;
                case CountryMaster.ALBANIA:
                    return CountryMasterType.ALBANIA;
                default:
                    throw new IllegalArgumentException("Invalid country: " + value);
            }
        }
    }
}
