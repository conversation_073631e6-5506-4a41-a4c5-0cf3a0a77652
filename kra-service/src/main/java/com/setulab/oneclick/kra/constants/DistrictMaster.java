package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * District Master constants and enum
 */
public class DistrictMaster {

    // Constants
    /**
     * District 1
     */
    public static final String DISTRICT_1 = "1";
    public static final String DISTRICT_1_DESC = "KRA";

    /**
     * District 2
     */
    public static final String DISTRICT_2 = "2";
    public static final String DISTRICT_2_DESC = "KRA";

    /**
     * District 3
     */
    public static final String DISTRICT_3 = "3";
    public static final String DISTRICT_3_DESC = "KRA";

    /**
     * District 4
     */
    public static final String DISTRICT_4 = "4";
    public static final String DISTRICT_4_DESC = "KRA";

    /**
     * District 5
     */
    public static final String DISTRICT_5 = "5";
    public static final String DISTRICT_5_DESC = "KRA";

    /**
     * List of all district codes
     */
    public static final List<String> DISTRICT_CODES = Arrays.asList(
            DISTRICT_1, DISTRICT_2, DISTRICT_3, DISTRICT_4, DISTRICT_5);

    /**
     * List of district codes as enum
     */
    public static final List<DistrictMasterType> DISTRICT_CODES_AS_ENUM = Arrays.asList(
            DistrictMasterType.DISTRICT_1,
            DistrictMasterType.DISTRICT_2,
            DistrictMasterType.DISTRICT_3,
            DistrictMasterType.DISTRICT_4,
            DistrictMasterType.DISTRICT_5);

    /**
     * District Master Type enum
     */
    public enum DistrictMasterType {
        DISTRICT_1("KRA"),
        DISTRICT_2("KRA"),
        DISTRICT_3("KRA"),
        DISTRICT_4("KRA"),
        DISTRICT_5("KRA");

        private final String description;

        DistrictMasterType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case DISTRICT_1:
                    return DistrictMaster.DISTRICT_1;
                case DISTRICT_2:
                    return DistrictMaster.DISTRICT_2;
                case DISTRICT_3:
                    return DistrictMaster.DISTRICT_3;
                case DISTRICT_4:
                    return DistrictMaster.DISTRICT_4;
                case DISTRICT_5:
                    return DistrictMaster.DISTRICT_5;
                default:
                    throw new IllegalStateException("Unknown district type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the district type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<DistrictMasterType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case DistrictMaster.DISTRICT_1:
                    return Optional.of(DistrictMasterType.DISTRICT_1);
                case DistrictMaster.DISTRICT_2:
                    return Optional.of(DistrictMasterType.DISTRICT_2);
                case DistrictMaster.DISTRICT_3:
                    return Optional.of(DistrictMasterType.DISTRICT_3);
                case DistrictMaster.DISTRICT_4:
                    return Optional.of(DistrictMasterType.DISTRICT_4);
                case DistrictMaster.DISTRICT_5:
                    return Optional.of(DistrictMasterType.DISTRICT_5);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse district type from string
         *
         * @param value the string value
         * @return the district type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static DistrictMasterType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case DistrictMaster.DISTRICT_1:
                    return DistrictMasterType.DISTRICT_1;
                case DistrictMaster.DISTRICT_2:
                    return DistrictMasterType.DISTRICT_2;
                case DistrictMaster.DISTRICT_3:
                    return DistrictMasterType.DISTRICT_3;
                case DistrictMaster.DISTRICT_4:
                    return DistrictMasterType.DISTRICT_4;
                case DistrictMaster.DISTRICT_5:
                    return DistrictMasterType.DISTRICT_5;
                default:
                    throw new IllegalArgumentException("Invalid district: " + value);
            }
        }
    }
}
