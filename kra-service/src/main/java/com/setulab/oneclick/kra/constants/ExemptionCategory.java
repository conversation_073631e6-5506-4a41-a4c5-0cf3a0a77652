package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Exemption Category constants and enum
 */
public class ExemptionCategory {

    // Constants
    /**
     * Sikkim Resident
     */
    public static final String SIKKIM_RESIDENT = "SIKKIM_RESIDENT";
    public static final String SIKKIM_RESIDENT_CODE = "01";
    public static final String SIKKIM_RESIDENT_DESC = "Sikkim Resident";

    /**
     * State Government
     */
    public static final String STATE_GOVT = "STATE_GOVT";
    public static final String STATE_GOVT_CODE = "02";
    public static final String STATE_GOVT_DESC = "State Government";

    /**
     * Central Government
     */
    public static final String CENTRAL_GOVT = "CENTRAL_GOVT";
    public static final String CENTRAL_GOVT_CODE = "03";
    public static final String CENTRAL_GOVT_DESC = "Central Government";

    /**
     * Court Appointed Officials
     */
    public static final String COURT_APPOINTED_OFFICIALS = "COURT_APPOINTED_OFFICIALS";
    public static final String COURT_APPOINTED_OFFICIALS_CODE = "04";
    public static final String COURT_APPOINTED_OFFICIALS_DESC = "Court Appointed Officials";

    /**
     * UN Entity / Multilateral Agency
     */
    public static final String UN_ENTITY_MULTILATERAL_AGENCY = "UN_ENTITY_MULTILATERAL_AGENCY";
    public static final String UN_ENTITY_MULTILATERAL_AGENCY_CODE = "05";
    public static final String UN_ENTITY_MULTILATERAL_AGENCY_DESC = "UN Entity / Multilateral Agency";

    /**
     * Official Liquidator
     */
    public static final String OFFICIAL_LIQUIDATOR = "OFFICIAL_LIQUIDATOR";
    public static final String OFFICIAL_LIQUIDATOR_CODE = "06";
    public static final String OFFICIAL_LIQUIDATOR_DESC = "Official Liquidator";

    /**
     * Court Receiver
     */
    public static final String COURT_RECEIVER = "COURT_RECEIVER";
    public static final String COURT_RECEIVER_CODE = "07";
    public static final String COURT_RECEIVER_DESC = "Court Receiver";

    /**
     * SIP of Mutual Funds upto 50K
     */
    public static final String SIP_OF_MUTUAL_FUNDS_UPTO_50K = "SIP_OF_MUTUAL_FUNDS_UPTO_50K";
    public static final String SIP_OF_MUTUAL_FUNDS_UPTO_50K_CODE = "08";
    public static final String SIP_OF_MUTUAL_FUNDS_UPTO_50K_DESC = "SIP of Mutual Funds upto 50K";

    /**
     * Other Documents
     */
    public static final String OTHER_DOCUMENTS = "OTHER_DOCUMENTS";
    public static final String OTHER_DOCUMENTS_CODE = "11";
    public static final String OTHER_DOCUMENTS_DESC = "Other Documents";

    /**
     * List of all exemption category codes
     */
    public static final List<String> EXEMPTION_CATEGORY_CODES = Arrays.asList(
            SIKKIM_RESIDENT_CODE, STATE_GOVT_CODE, CENTRAL_GOVT_CODE, COURT_APPOINTED_OFFICIALS_CODE,
            UN_ENTITY_MULTILATERAL_AGENCY_CODE, OFFICIAL_LIQUIDATOR_CODE, COURT_RECEIVER_CODE,
            SIP_OF_MUTUAL_FUNDS_UPTO_50K_CODE, OTHER_DOCUMENTS_CODE);

    /**
     * List of exemption category codes as enum
     */
    public static final List<ExemptionCategoryType> EXEMPTION_CATEGORY_CODES_AS_ENUM = Arrays.asList(
            ExemptionCategoryType.SIKKIM_RESIDENT,
            ExemptionCategoryType.STATE_GOVT,
            ExemptionCategoryType.CENTRAL_GOVT,
            ExemptionCategoryType.COURT_APPOINTED_OFFICIALS,
            ExemptionCategoryType.UN_ENTITY_MULTILATERAL_AGENCY,
            ExemptionCategoryType.OFFICIAL_LIQUIDATOR,
            ExemptionCategoryType.COURT_RECEIVER,
            ExemptionCategoryType.SIP_OF_MUTUAL_FUNDS_UPTO_50K,
            ExemptionCategoryType.OTHER_DOCUMENTS);

    /**
     * Exemption Category Type enum
     */
    public enum ExemptionCategoryType {
        SIKKIM_RESIDENT("Sikkim Resident"),
        STATE_GOVT("State Government"),
        CENTRAL_GOVT("Central Government"),
        COURT_APPOINTED_OFFICIALS("Court Appointed Officials"),
        UN_ENTITY_MULTILATERAL_AGENCY("UN Entity / Multilateral Agency"),
        OFFICIAL_LIQUIDATOR("Official Liquidator"),
        COURT_RECEIVER("Court Receiver"),
        SIP_OF_MUTUAL_FUNDS_UPTO_50K("SIP of Mutual Funds upto 50K"),
        OTHER_DOCUMENTS("Other Documents");

        private final String description;

        ExemptionCategoryType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case SIKKIM_RESIDENT:
                    return SIKKIM_RESIDENT_CODE;
                case STATE_GOVT:
                    return STATE_GOVT_CODE;
                case CENTRAL_GOVT:
                    return CENTRAL_GOVT_CODE;
                case COURT_APPOINTED_OFFICIALS:
                    return COURT_APPOINTED_OFFICIALS_CODE;
                case UN_ENTITY_MULTILATERAL_AGENCY:
                    return UN_ENTITY_MULTILATERAL_AGENCY_CODE;
                case OFFICIAL_LIQUIDATOR:
                    return OFFICIAL_LIQUIDATOR_CODE;
                case COURT_RECEIVER:
                    return COURT_RECEIVER_CODE;
                case SIP_OF_MUTUAL_FUNDS_UPTO_50K:
                    return SIP_OF_MUTUAL_FUNDS_UPTO_50K_CODE;
                case OTHER_DOCUMENTS:
                    return OTHER_DOCUMENTS_CODE;
                default:
                    throw new IllegalStateException("Unknown exemption category type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the exemption category type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<ExemptionCategoryType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case SIKKIM_RESIDENT_CODE:
                    return Optional.of(ExemptionCategoryType.SIKKIM_RESIDENT);
                case STATE_GOVT_CODE:
                    return Optional.of(ExemptionCategoryType.STATE_GOVT);
                case CENTRAL_GOVT_CODE:
                    return Optional.of(ExemptionCategoryType.CENTRAL_GOVT);
                case COURT_APPOINTED_OFFICIALS_CODE:
                    return Optional.of(ExemptionCategoryType.COURT_APPOINTED_OFFICIALS);
                case UN_ENTITY_MULTILATERAL_AGENCY_CODE:
                    return Optional.of(ExemptionCategoryType.UN_ENTITY_MULTILATERAL_AGENCY);
                case OFFICIAL_LIQUIDATOR_CODE:
                    return Optional.of(ExemptionCategoryType.OFFICIAL_LIQUIDATOR);
                case COURT_RECEIVER_CODE:
                    return Optional.of(ExemptionCategoryType.COURT_RECEIVER);
                case SIP_OF_MUTUAL_FUNDS_UPTO_50K_CODE:
                    return Optional.of(ExemptionCategoryType.SIP_OF_MUTUAL_FUNDS_UPTO_50K);
                case OTHER_DOCUMENTS_CODE:
                    return Optional.of(ExemptionCategoryType.OTHER_DOCUMENTS);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse exemption category type from string
         *
         * @param value the string value
         * @return the exemption category type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static ExemptionCategoryType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case ExemptionCategory.SIKKIM_RESIDENT:
                    return ExemptionCategoryType.SIKKIM_RESIDENT;
                case ExemptionCategory.STATE_GOVT:
                    return ExemptionCategoryType.STATE_GOVT;
                case ExemptionCategory.CENTRAL_GOVT:
                    return ExemptionCategoryType.CENTRAL_GOVT;
                case ExemptionCategory.COURT_APPOINTED_OFFICIALS:
                    return ExemptionCategoryType.COURT_APPOINTED_OFFICIALS;
                case ExemptionCategory.UN_ENTITY_MULTILATERAL_AGENCY:
                    return ExemptionCategoryType.UN_ENTITY_MULTILATERAL_AGENCY;
                case ExemptionCategory.OFFICIAL_LIQUIDATOR:
                    return ExemptionCategoryType.OFFICIAL_LIQUIDATOR;
                case ExemptionCategory.COURT_RECEIVER:
                    return ExemptionCategoryType.COURT_RECEIVER;
                case ExemptionCategory.SIP_OF_MUTUAL_FUNDS_UPTO_50K:
                    return ExemptionCategoryType.SIP_OF_MUTUAL_FUNDS_UPTO_50K;
                case ExemptionCategory.OTHER_DOCUMENTS:
                    return ExemptionCategoryType.OTHER_DOCUMENTS;
                default:
                    throw new IllegalArgumentException("Invalid exemption category: " + value);
            }
        }
    }
}
