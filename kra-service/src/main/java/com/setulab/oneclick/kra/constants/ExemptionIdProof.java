package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Exemption ID Proof constants and enum
 */
public class ExemptionIdProof {

    // Constants
    /**
     * PAN
     */
    public static final String PAN = "PAN";
    public static final String PAN_CODE = "01";
    public static final String PAN_DESC = "PAN";

    /**
     * UID Number
     */
    public static final String UID_NO = "UID_NO";
    public static final String UID_NO_CODE = "02";
    public static final String UID_NO_DESC = "UID Number";

    /**
     * Passport
     */
    public static final String PASSPORT = "PASSPORT";
    public static final String PASSPORT_CODE = "03";
    public static final String PASSPORT_DESC = "Passport";

    /**
     * Driving License
     */
    public static final String DRIVING_LICENSE = "DRIVING_LICENSE";
    public static final String DRIVING_LICENSE_CODE = "04";
    public static final String DRIVING_LICENSE_DESC = "Driving License";

    /**
     * Voter Identity Card
     */
    public static final String VOTER_IDENTITY_CARD = "VOTER_IDENTITY_CARD";
    public static final String VOTER_IDENTITY_CARD_CODE = "05";
    public static final String VOTER_IDENTITY_CARD_DESC = "Voter Identity Card";

    /**
     * ID Card with Applicant Photo issued by Central/State Government and its Departments
     */
    public static final String CENTRAL_STATE_GOVT_ID_CARD = "CENTRAL_STATE_GOVT_ID_CARD";
    public static final String CENTRAL_STATE_GOVT_ID_CARD_CODE = "06";
    public static final String CENTRAL_STATE_GOVT_ID_CARD_DESC = "ID Card with Applicant Photo issued by Central/State Government and its Departments";

    /**
     * ID Card with Applicant Photo issued by Statutory/Regulatory Authorities
     */
    public static final String STATUTORY_REGULATORY_ID_CARD = "STATUTORY_REGULATORY_ID_CARD";
    public static final String STATUTORY_REGULATORY_ID_CARD_CODE = "07";
    public static final String STATUTORY_REGULATORY_ID_CARD_DESC = "ID Card with Applicant Photo issued by Statutory/Regulatory Authorities";

    /**
     * ID Card with Applicant Photo issued by Public Sector Undertakings
     */
    public static final String PUBLIC_SECTOR_ID_CARD = "PUBLIC_SECTOR_ID_CARD";
    public static final String PUBLIC_SECTOR_ID_CARD_CODE = "08";
    public static final String PUBLIC_SECTOR_ID_CARD_DESC = "ID Card with Applicant Photo issued by Public Sector Undertakings";

    /**
     * ID Card with Applicant Photo issued by Scheduled Commercial Banks
     */
    public static final String COMMERCIAL_BANK_ID_CARD = "COMMERCIAL_BANK_ID_CARD";
    public static final String COMMERCIAL_BANK_ID_CARD_CODE = "09";
    public static final String COMMERCIAL_BANK_ID_CARD_DESC = "ID Card with Applicant Photo issued by Scheduled Commercial Banks";

    /**
     * ID Card with Applicant Photo issued by Public Financial Institutions
     */
    public static final String FINANCIAL_INSTITUTION_ID_CARD = "FINANCIAL_INSTITUTION_ID_CARD";
    public static final String FINANCIAL_INSTITUTION_ID_CARD_CODE = "10";
    public static final String FINANCIAL_INSTITUTION_ID_CARD_DESC = "ID Card with Applicant Photo issued by Public Financial Institutions";

    /**
     * ID Card with Applicant Photo issued by Colleges affiliated to Universities
     */
    public static final String COLLEGE_ID_CARD = "COLLEGE_ID_CARD";
    public static final String COLLEGE_ID_CARD_CODE = "11";
    public static final String COLLEGE_ID_CARD_DESC = "ID Card with Applicant Photo issued by Colleges affiliated to Universities";

    /**
     * ID Card issued by Professional Bodies such as ICAI, ICWAI, ICSI, Bar Council etc. to their members
     */
    public static final String PROFESSIONAL_BODY_ID_CARD = "PROFESSIONAL_BODY_ID_CARD";
    public static final String PROFESSIONAL_BODY_ID_CARD_CODE = "12";
    public static final String PROFESSIONAL_BODY_ID_CARD_DESC = "ID Card issued by Professional Bodies such as ICAI, ICWAI, ICSI, Bar Council etc. to their members";

    /**
     * Credit Card with Photo of Applicant issued by Banks
     */
    public static final String CREDIT_CARD_WITH_PHOTO = "CREDIT_CARD_WITH_PHOTO";
    public static final String CREDIT_CARD_WITH_PHOTO_CODE = "13";
    public static final String CREDIT_CARD_WITH_PHOTO_DESC = "Credit Card with Photo of Applicant issued by Banks";

    /**
     * Other ID Proof
     */
    public static final String OTHER_ID_PROOF = "OTHER_ID_PROOF";
    public static final String OTHER_ID_PROOF_CODE = "16";
    public static final String OTHER_ID_PROOF_DESC = "Other ID Proof";

    /**
     * List of all exemption ID proof codes
     */
    public static final List<String> EXEMPTION_ID_PROOF_CODES = Arrays.asList(
            PAN_CODE, UID_NO_CODE, PASSPORT_CODE, DRIVING_LICENSE_CODE, VOTER_IDENTITY_CARD_CODE,
            CENTRAL_STATE_GOVT_ID_CARD_CODE, STATUTORY_REGULATORY_ID_CARD_CODE, PUBLIC_SECTOR_ID_CARD_CODE,
            COMMERCIAL_BANK_ID_CARD_CODE, FINANCIAL_INSTITUTION_ID_CARD_CODE, COLLEGE_ID_CARD_CODE,
            PROFESSIONAL_BODY_ID_CARD_CODE, CREDIT_CARD_WITH_PHOTO_CODE, OTHER_ID_PROOF_CODE);

    /**
     * List of exemption ID proof codes as enum
     */
    public static final List<ExemptionIdProofType> EXEMPTION_ID_PROOF_CODES_AS_ENUM = Arrays.asList(
            ExemptionIdProofType.PAN,
            ExemptionIdProofType.UID_NO,
            ExemptionIdProofType.PASSPORT,
            ExemptionIdProofType.DRIVING_LICENSE,
            ExemptionIdProofType.VOTER_IDENTITY_CARD,
            ExemptionIdProofType.CENTRAL_STATE_GOVT_ID_CARD,
            ExemptionIdProofType.STATUTORY_REGULATORY_ID_CARD,
            ExemptionIdProofType.PUBLIC_SECTOR_ID_CARD,
            ExemptionIdProofType.COMMERCIAL_BANK_ID_CARD,
            ExemptionIdProofType.FINANCIAL_INSTITUTION_ID_CARD,
            ExemptionIdProofType.COLLEGE_ID_CARD,
            ExemptionIdProofType.PROFESSIONAL_BODY_ID_CARD,
            ExemptionIdProofType.CREDIT_CARD_WITH_PHOTO,
            ExemptionIdProofType.OTHER_ID_PROOF);

    /**
     * Exemption ID Proof Type enum
     */
    public enum ExemptionIdProofType {
        PAN("PAN"),
        UID_NO("UID Number"),
        PASSPORT("Passport"),
        DRIVING_LICENSE("Driving License"),
        VOTER_IDENTITY_CARD("Voter Identity Card"),
        CENTRAL_STATE_GOVT_ID_CARD("ID Card with Applicant Photo issued by Central/State Government and its Departments"),
        STATUTORY_REGULATORY_ID_CARD("ID Card with Applicant Photo issued by Statutory/Regulatory Authorities"),
        PUBLIC_SECTOR_ID_CARD("ID Card with Applicant Photo issued by Public Sector Undertakings"),
        COMMERCIAL_BANK_ID_CARD("ID Card with Applicant Photo issued by Scheduled Commercial Banks"),
        FINANCIAL_INSTITUTION_ID_CARD("ID Card with Applicant Photo issued by Public Financial Institutions"),
        COLLEGE_ID_CARD("ID Card with Applicant Photo issued by Colleges affiliated to Universities"),
        PROFESSIONAL_BODY_ID_CARD("ID Card issued by Professional Bodies such as ICAI, ICWAI, ICSI, Bar Council etc. to their members"),
        CREDIT_CARD_WITH_PHOTO("Credit Card with Photo of Applicant issued by Banks"),
        OTHER_ID_PROOF("Other ID Proof");

        private final String description;

        ExemptionIdProofType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case PAN:
                    return PAN_CODE;
                case UID_NO:
                    return UID_NO_CODE;
                case PASSPORT:
                    return PASSPORT_CODE;
                case DRIVING_LICENSE:
                    return DRIVING_LICENSE_CODE;
                case VOTER_IDENTITY_CARD:
                    return VOTER_IDENTITY_CARD_CODE;
                case CENTRAL_STATE_GOVT_ID_CARD:
                    return CENTRAL_STATE_GOVT_ID_CARD_CODE;
                case STATUTORY_REGULATORY_ID_CARD:
                    return STATUTORY_REGULATORY_ID_CARD_CODE;
                case PUBLIC_SECTOR_ID_CARD:
                    return PUBLIC_SECTOR_ID_CARD_CODE;
                case COMMERCIAL_BANK_ID_CARD:
                    return COMMERCIAL_BANK_ID_CARD_CODE;
                case FINANCIAL_INSTITUTION_ID_CARD:
                    return FINANCIAL_INSTITUTION_ID_CARD_CODE;
                case COLLEGE_ID_CARD:
                    return COLLEGE_ID_CARD_CODE;
                case PROFESSIONAL_BODY_ID_CARD:
                    return PROFESSIONAL_BODY_ID_CARD_CODE;
                case CREDIT_CARD_WITH_PHOTO:
                    return CREDIT_CARD_WITH_PHOTO_CODE;
                case OTHER_ID_PROOF:
                    return OTHER_ID_PROOF_CODE;
                default:
                    throw new IllegalStateException("Unknown exemption ID proof type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the exemption ID proof type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<ExemptionIdProofType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case PAN_CODE:
                    return Optional.of(ExemptionIdProofType.PAN);
                case UID_NO_CODE:
                    return Optional.of(ExemptionIdProofType.UID_NO);
                case PASSPORT_CODE:
                    return Optional.of(ExemptionIdProofType.PASSPORT);
                case DRIVING_LICENSE_CODE:
                    return Optional.of(ExemptionIdProofType.DRIVING_LICENSE);
                case VOTER_IDENTITY_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.VOTER_IDENTITY_CARD);
                case CENTRAL_STATE_GOVT_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.CENTRAL_STATE_GOVT_ID_CARD);
                case STATUTORY_REGULATORY_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.STATUTORY_REGULATORY_ID_CARD);
                case PUBLIC_SECTOR_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.PUBLIC_SECTOR_ID_CARD);
                case COMMERCIAL_BANK_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.COMMERCIAL_BANK_ID_CARD);
                case FINANCIAL_INSTITUTION_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.FINANCIAL_INSTITUTION_ID_CARD);
                case COLLEGE_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.COLLEGE_ID_CARD);
                case PROFESSIONAL_BODY_ID_CARD_CODE:
                    return Optional.of(ExemptionIdProofType.PROFESSIONAL_BODY_ID_CARD);
                case CREDIT_CARD_WITH_PHOTO_CODE:
                    return Optional.of(ExemptionIdProofType.CREDIT_CARD_WITH_PHOTO);
                case OTHER_ID_PROOF_CODE:
                    return Optional.of(ExemptionIdProofType.OTHER_ID_PROOF);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse exemption ID proof type from string
         *
         * @param value the string value
         * @return the exemption ID proof type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static ExemptionIdProofType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case ExemptionIdProof.PAN:
                    return ExemptionIdProofType.PAN;
                case ExemptionIdProof.UID_NO:
                    return ExemptionIdProofType.UID_NO;
                case ExemptionIdProof.PASSPORT:
                    return ExemptionIdProofType.PASSPORT;
                case ExemptionIdProof.DRIVING_LICENSE:
                    return ExemptionIdProofType.DRIVING_LICENSE;
                case ExemptionIdProof.VOTER_IDENTITY_CARD:
                    return ExemptionIdProofType.VOTER_IDENTITY_CARD;
                case ExemptionIdProof.CENTRAL_STATE_GOVT_ID_CARD:
                    return ExemptionIdProofType.CENTRAL_STATE_GOVT_ID_CARD;
                case ExemptionIdProof.STATUTORY_REGULATORY_ID_CARD:
                    return ExemptionIdProofType.STATUTORY_REGULATORY_ID_CARD;
                case ExemptionIdProof.PUBLIC_SECTOR_ID_CARD:
                    return ExemptionIdProofType.PUBLIC_SECTOR_ID_CARD;
                case ExemptionIdProof.COMMERCIAL_BANK_ID_CARD:
                    return ExemptionIdProofType.COMMERCIAL_BANK_ID_CARD;
                case ExemptionIdProof.FINANCIAL_INSTITUTION_ID_CARD:
                    return ExemptionIdProofType.FINANCIAL_INSTITUTION_ID_CARD;
                case ExemptionIdProof.COLLEGE_ID_CARD:
                    return ExemptionIdProofType.COLLEGE_ID_CARD;
                case ExemptionIdProof.PROFESSIONAL_BODY_ID_CARD:
                    return ExemptionIdProofType.PROFESSIONAL_BODY_ID_CARD;
                case ExemptionIdProof.CREDIT_CARD_WITH_PHOTO:
                    return ExemptionIdProofType.CREDIT_CARD_WITH_PHOTO;
                case ExemptionIdProof.OTHER_ID_PROOF:
                    return ExemptionIdProofType.OTHER_ID_PROOF;
                default:
                    throw new IllegalArgumentException("Invalid exemption ID proof: " + value);
            }
        }
    }
}
