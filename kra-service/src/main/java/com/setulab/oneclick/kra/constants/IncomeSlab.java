package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Income Slab constants and enum
 */
public class IncomeSlab {

    // Constants
    /**
     * Below 1 Lac
     */
    public static final String BELOW_1_LAC = "<1L";
    public static final String BELOW_1_LAC_CODE = "01";
    public static final String BELOW_1_LAC_DESC = "Below 1 Lac";

    /**
     * 1 - 5 Lac
     */
    public static final String ONE_TO_FIVE_LAC = "1L-5L";
    public static final String ONE_TO_FIVE_LAC_CODE = "02";
    public static final String ONE_TO_FIVE_LAC_DESC = "1 - 5 Lac";

    /**
     * 5 - 10 Lac
     */
    public static final String FIVE_TO_TEN_LAC = "5L-10L";
    public static final String FIVE_TO_TEN_LAC_CODE = "03";
    public static final String FIVE_TO_TEN_LAC_DESC = "5 - 10 Lac";

    /**
     * 10 - 25 Lac
     */
    public static final String TEN_TO_TWENTY_FIVE_LAC = "10L-25L";
    public static final String TEN_TO_TWENTY_FIVE_LAC_CODE = "04";
    public static final String TEN_TO_TWENTY_FIVE_LAC_DESC = "10 - 25 Lac";

    /**
     * Above 25 Lac (Only for Individuals)
     */
    public static final String ABOVE_TWENTY_FIVE_LAC = ">25L";
    public static final String ABOVE_TWENTY_FIVE_LAC_CODE = "05";
    public static final String ABOVE_TWENTY_FIVE_LAC_DESC = "Above 25 Lac";

    /**
     * 25 Lac - 1 Crore (Available only for non-individuals)
     */
    public static final String TWENTY_FIVE_LAC_TO_ONE_CRORE = "25L-1CR";
    public static final String TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE = "06";
    public static final String TWENTY_FIVE_LAC_TO_ONE_CRORE_DESC = "25 Lac - 1 Crore";

    /**
     * Above 1 Crore (Available only for non-individuals)
     */
    public static final String ABOVE_ONE_CRORE = ">1CR";
    public static final String ABOVE_ONE_CRORE_CODE = "07";
    public static final String ABOVE_ONE_CRORE_DESC = "Above 1 Crore";

    /**
     * List of all income slab codes
     */
    public static final List<String> INCOME_SLAB_CODES = Arrays.asList(
            BELOW_1_LAC_CODE, ONE_TO_FIVE_LAC_CODE, FIVE_TO_TEN_LAC_CODE, TEN_TO_TWENTY_FIVE_LAC_CODE,
            ABOVE_TWENTY_FIVE_LAC_CODE, TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE, ABOVE_ONE_CRORE_CODE);

    /**
     * List of income slab codes as enum
     */
    public static final List<IncomeSlabType> INCOME_SLAB_CODES_AS_ENUM = Arrays.asList(
            IncomeSlabType.BELOW_1_LAC,
            IncomeSlabType.ONE_TO_FIVE_LAC,
            IncomeSlabType.FIVE_TO_TEN_LAC,
            IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC,
            IncomeSlabType.ABOVE_TWENTY_FIVE_LAC,
            IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE,
            IncomeSlabType.ABOVE_ONE_CRORE);

    /**
     * Income Slab Type enum
     */
    public enum IncomeSlabType {
        BELOW_1_LAC("Below 1 Lac"),
        ONE_TO_FIVE_LAC("1 - 5 Lac"),
        FIVE_TO_TEN_LAC("5 - 10 Lac"),
        TEN_TO_TWENTY_FIVE_LAC("10 - 25 Lac"),
        ABOVE_TWENTY_FIVE_LAC("Above 25 Lac"), // Only for Individuals
        TWENTY_FIVE_LAC_TO_ONE_CRORE("25 Lac - 1 Crore"), // Available only for non-individuals
        ABOVE_ONE_CRORE("Above 1 Crore"); // Available only for non-individuals

        private final String description;

        IncomeSlabType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case BELOW_1_LAC:
                    return BELOW_1_LAC_CODE;
                case ONE_TO_FIVE_LAC:
                    return ONE_TO_FIVE_LAC_CODE;
                case FIVE_TO_TEN_LAC:
                    return FIVE_TO_TEN_LAC_CODE;
                case TEN_TO_TWENTY_FIVE_LAC:
                    return TEN_TO_TWENTY_FIVE_LAC_CODE;
                case ABOVE_TWENTY_FIVE_LAC:
                    return ABOVE_TWENTY_FIVE_LAC_CODE;
                case TWENTY_FIVE_LAC_TO_ONE_CRORE:
                    return TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE;
                case ABOVE_ONE_CRORE:
                    return ABOVE_ONE_CRORE_CODE;
                default:
                    throw new IllegalStateException("Unknown income slab type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Check if the income slab is available for individuals
         *
         * @return true if available for individuals
         */
        public boolean isAvailableForIndividuals() {
            switch (this) {
                case BELOW_1_LAC:
                case ONE_TO_FIVE_LAC:
                case FIVE_TO_TEN_LAC:
                case TEN_TO_TWENTY_FIVE_LAC:
                case ABOVE_TWENTY_FIVE_LAC:
                    return true;
                case TWENTY_FIVE_LAC_TO_ONE_CRORE:
                case ABOVE_ONE_CRORE:
                    return false;
                default:
                    return false;
            }
        }

        /**
         * Check if the income slab is available for non-individuals
         *
         * @return true if available for non-individuals
         */
        public boolean isAvailableForNonIndividuals() {
            switch (this) {
                case BELOW_1_LAC:
                case ONE_TO_FIVE_LAC:
                case FIVE_TO_TEN_LAC:
                case TEN_TO_TWENTY_FIVE_LAC:
                case TWENTY_FIVE_LAC_TO_ONE_CRORE:
                case ABOVE_ONE_CRORE:
                    return true;
                case ABOVE_TWENTY_FIVE_LAC:
                    return false;
                default:
                    return false;
            }
        }

        /**
         * Get the income slab type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<IncomeSlabType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case BELOW_1_LAC_CODE:
                    return Optional.of(IncomeSlabType.BELOW_1_LAC);
                case ONE_TO_FIVE_LAC_CODE:
                    return Optional.of(IncomeSlabType.ONE_TO_FIVE_LAC);
                case FIVE_TO_TEN_LAC_CODE:
                    return Optional.of(IncomeSlabType.FIVE_TO_TEN_LAC);
                case TEN_TO_TWENTY_FIVE_LAC_CODE:
                    return Optional.of(IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC);
                case ABOVE_TWENTY_FIVE_LAC_CODE:
                    return Optional.of(IncomeSlabType.ABOVE_TWENTY_FIVE_LAC);
                case TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE:
                    return Optional.of(IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE);
                case ABOVE_ONE_CRORE_CODE:
                    return Optional.of(IncomeSlabType.ABOVE_ONE_CRORE);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse income slab type from string
         *
         * @param value the string value
         * @return the income slab type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static IncomeSlabType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case IncomeSlab.BELOW_1_LAC:
                    return IncomeSlabType.BELOW_1_LAC;
                case IncomeSlab.ONE_TO_FIVE_LAC:
                    return IncomeSlabType.ONE_TO_FIVE_LAC;
                case IncomeSlab.FIVE_TO_TEN_LAC:
                    return IncomeSlabType.FIVE_TO_TEN_LAC;
                case IncomeSlab.TEN_TO_TWENTY_FIVE_LAC:
                    return IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC;
                case IncomeSlab.ABOVE_TWENTY_FIVE_LAC:
                    return IncomeSlabType.ABOVE_TWENTY_FIVE_LAC;
                case IncomeSlab.TWENTY_FIVE_LAC_TO_ONE_CRORE:
                    return IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE;
                case IncomeSlab.ABOVE_ONE_CRORE:
                    return IncomeSlabType.ABOVE_ONE_CRORE;
                default:
                    throw new IllegalArgumentException("Invalid income slab: " + value);
            }
        }
    }
}
