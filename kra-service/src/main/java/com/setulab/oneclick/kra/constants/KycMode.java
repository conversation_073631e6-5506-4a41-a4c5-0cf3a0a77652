package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * KYC Mode constants and enum
 */
public class KycMode {

    // Constants
    /**
     * Normal KYC
     */
    public static final String NORMAL_KYC = "NORMAL_KYC";
    public static final String NORMAL_KYC_CODE = "0";
    public static final String NORMAL_KYC_DESC = "Normal KYC";

    /**
     * e-KYC with OTP (Applicable for New Individual only with UID No Mandatory)
     */
    public static final String E_KYC_OTP = "E_KYC_OTP";
    public static final String E_KYC_OTP_CODE = "1";
    public static final String E_KYC_OTP_DESC = "e-KYC with OTP";

    /**
     * e-KYC with Biometric (Applicable for New Individual only with UID No Mandatory)
     */
    public static final String E_KYC_BIOMETRIC = "E_KYC_BIOMETRIC";
    public static final String E_KYC_BIOMETRIC_CODE = "2";
    public static final String E_KYC_BIOMETRIC_DESC = "e-KYC with Biometric";

    /**
     * Online Data Entry and IPV
     */
    public static final String ONLINE_DATA_ENTRY_AND_IPV = "ONLINE_DATA_ENTRY_AND_IPV";
    public static final String ONLINE_DATA_ENTRY_AND_IPV_CODE = "3";
    public static final String ONLINE_DATA_ENTRY_AND_IPV_DESC = "Online Data Entry and IPV";

    /**
     * Offline KYC - Aadhaar (Applicable for New Individual only with UID No Mandatory)
     */
    public static final String OFFLINE_KYC_AADHAAR = "OFFLINE_KYC_AADHAAR";
    public static final String OFFLINE_KYC_AADHAAR_CODE = "4";
    public static final String OFFLINE_KYC_AADHAAR_DESC = "Offline KYC - Aadhaar";

    /**
     * Digilocker (Applicable for New Individual only with UID No Mandatory)
     */
    public static final String DIGILOCKER = "DIGILOCKER";
    public static final String DIGILOCKER_CODE = "5";
    public static final String DIGILOCKER_DESC = "Digilocker";

    /**
     * SARAL
     */
    public static final String SARAL = "SARAL";
    public static final String SARAL_CODE = "6";
    public static final String SARAL_DESC = "SARAL";

    /**
     * List of all KYC mode codes
     */
    public static final List<String> KYC_MODE_CODES = Arrays.asList(
            NORMAL_KYC_CODE, E_KYC_OTP_CODE, E_KYC_BIOMETRIC_CODE, ONLINE_DATA_ENTRY_AND_IPV_CODE,
            OFFLINE_KYC_AADHAAR_CODE, DIGILOCKER_CODE, SARAL_CODE);

    /**
     * List of KYC mode codes as enum
     */
    public static final List<KycModeType> KYC_MODE_CODES_AS_ENUM = Arrays.asList(
            KycModeType.NORMAL_KYC,
            KycModeType.E_KYC_OTP,
            KycModeType.E_KYC_BIOMETRIC,
            KycModeType.ONLINE_DATA_ENTRY_AND_IPV,
            KycModeType.OFFLINE_KYC_AADHAAR,
            KycModeType.DIGILOCKER,
            KycModeType.SARAL);

    /**
     * KYC Mode Type enum
     */
    public enum KycModeType {
        NORMAL_KYC("Normal KYC"),
        E_KYC_OTP("e-KYC with OTP"), // Applicable for New Individual only with UID No Mandatory
        E_KYC_BIOMETRIC("e-KYC with Biometric"), // Applicable for New Individual only with UID No Mandatory
        ONLINE_DATA_ENTRY_AND_IPV("Online Data Entry and IPV"),
        OFFLINE_KYC_AADHAAR("Offline KYC - Aadhaar"), // Applicable for New Individual only with UID No Mandatory
        DIGILOCKER("Digilocker"), // Applicable for New Individual only with UID No Mandatory
        SARAL("SARAL");

        private final String description;

        KycModeType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case NORMAL_KYC:
                    return NORMAL_KYC_CODE;
                case E_KYC_OTP:
                    return E_KYC_OTP_CODE;
                case E_KYC_BIOMETRIC:
                    return E_KYC_BIOMETRIC_CODE;
                case ONLINE_DATA_ENTRY_AND_IPV:
                    return ONLINE_DATA_ENTRY_AND_IPV_CODE;
                case OFFLINE_KYC_AADHAAR:
                    return OFFLINE_KYC_AADHAAR_CODE;
                case DIGILOCKER:
                    return DIGILOCKER_CODE;
                case SARAL:
                    return SARAL_CODE;
                default:
                    throw new IllegalStateException("Unknown KYC mode type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Check if the KYC mode requires UID number and is only for new individuals
         *
         * @return true if requires UID and is for new individuals only
         */
        public boolean requiresUidAndNewIndividual() {
            switch (this) {
                case E_KYC_OTP:
                case E_KYC_BIOMETRIC:
                case OFFLINE_KYC_AADHAAR:
                case DIGILOCKER:
                    return true;
                case NORMAL_KYC:
                case ONLINE_DATA_ENTRY_AND_IPV:
                case SARAL:
                    return false;
                default:
                    return false;
            }
        }

        /**
         * Get the KYC mode type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<KycModeType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case NORMAL_KYC_CODE:
                    return Optional.of(KycModeType.NORMAL_KYC);
                case E_KYC_OTP_CODE:
                    return Optional.of(KycModeType.E_KYC_OTP);
                case E_KYC_BIOMETRIC_CODE:
                    return Optional.of(KycModeType.E_KYC_BIOMETRIC);
                case ONLINE_DATA_ENTRY_AND_IPV_CODE:
                    return Optional.of(KycModeType.ONLINE_DATA_ENTRY_AND_IPV);
                case OFFLINE_KYC_AADHAAR_CODE:
                    return Optional.of(KycModeType.OFFLINE_KYC_AADHAAR);
                case DIGILOCKER_CODE:
                    return Optional.of(KycModeType.DIGILOCKER);
                case SARAL_CODE:
                    return Optional.of(KycModeType.SARAL);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse KYC mode type from string
         *
         * @param value the string value
         * @return the KYC mode type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static KycModeType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case KycMode.NORMAL_KYC:
                    return KycModeType.NORMAL_KYC;
                case KycMode.E_KYC_OTP:
                    return KycModeType.E_KYC_OTP;
                case KycMode.E_KYC_BIOMETRIC:
                    return KycModeType.E_KYC_BIOMETRIC;
                case KycMode.ONLINE_DATA_ENTRY_AND_IPV:
                    return KycModeType.ONLINE_DATA_ENTRY_AND_IPV;
                case KycMode.OFFLINE_KYC_AADHAAR:
                    return KycModeType.OFFLINE_KYC_AADHAAR;
                case KycMode.DIGILOCKER:
                    return KycModeType.DIGILOCKER;
                case KycMode.SARAL:
                    return KycModeType.SARAL;
                default:
                    throw new IllegalArgumentException("Invalid KYC mode: " + value);
            }
        }
    }
}
