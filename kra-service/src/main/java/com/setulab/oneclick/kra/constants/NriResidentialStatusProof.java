package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * NRI Residential Status Proof constants and enum
 */
public class NriResidentialStatusProof {

    // Constants
    /**
     * Passport
     */
    public static final String PASSPORT = "PASSPORT";
    public static final String PASSPORT_CODE = "01";
    public static final String PASSPORT_DESC = "Passport";

    /**
     * PIO Card
     */
    public static final String PIO_CARD = "PIO_CARD";
    public static final String PIO_CARD_CODE = "02";
    public static final String PIO_CARD_DESC = "PIO Card";

    /**
     * OCI Card
     */
    public static final String OCI_CARD = "OCI_CARD";
    public static final String OCI_CARD_CODE = "03";
    public static final String OCI_CARD_DESC = "OCI Card";

    /**
     * List of all NRI residential status proof codes
     */
    public static final List<String> NRI_RESIDENTIAL_STATUS_PROOF_CODES = Arrays.asList(
            PASSPORT_CODE, PIO_CARD_CODE, OCI_CARD_CODE);

    /**
     * List of NRI residential status proof codes as enum
     */
    public static final List<NriResidentialStatusProofType> NRI_RESIDENTIAL_STATUS_PROOF_CODES_AS_ENUM = Arrays.asList(
            NriResidentialStatusProofType.PASSPORT,
            NriResidentialStatusProofType.PIO_CARD,
            NriResidentialStatusProofType.OCI_CARD);

    /**
     * NRI Residential Status Proof Type enum
     */
    public enum NriResidentialStatusProofType {
        PASSPORT("Passport"),
        PIO_CARD("PIO Card"),
        OCI_CARD("OCI Card");

        private final String description;

        NriResidentialStatusProofType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case PASSPORT:
                    return PASSPORT_CODE;
                case PIO_CARD:
                    return PIO_CARD_CODE;
                case OCI_CARD:
                    return OCI_CARD_CODE;
                default:
                    throw new IllegalStateException("Unknown NRI residential status proof type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the NRI residential status proof type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<NriResidentialStatusProofType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case PASSPORT_CODE:
                    return Optional.of(NriResidentialStatusProofType.PASSPORT);
                case PIO_CARD_CODE:
                    return Optional.of(NriResidentialStatusProofType.PIO_CARD);
                case OCI_CARD_CODE:
                    return Optional.of(NriResidentialStatusProofType.OCI_CARD);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse NRI residential status proof type from string
         *
         * @param value the string value
         * @return the NRI residential status proof type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static NriResidentialStatusProofType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case NriResidentialStatusProof.PASSPORT:
                    return NriResidentialStatusProofType.PASSPORT;
                case NriResidentialStatusProof.PIO_CARD:
                    return NriResidentialStatusProofType.PIO_CARD;
                case NriResidentialStatusProof.OCI_CARD:
                    return NriResidentialStatusProofType.OCI_CARD;
                default:
                    throw new IllegalArgumentException("Invalid NRI residential status proof: " + value);
            }
        }
    }
}
