package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Occupation constants and enum
 */
public class Occupation {

    // Constants
    /**
     * Private Sector Service
     */
    public static final String PRIVATE_SECTOR_SERVICE = "PRIVATE_SECTOR_SERVICE";
    public static final String PRIVATE_SECTOR_SERVICE_CODE = "01";
    public static final String PRIVATE_SECTOR_SERVICE_DESC = "Private Sector Service";

    /**
     * Public Sector Service
     */
    public static final String PUBLIC_SECTOR_SERVICE = "PUBLIC_SECTOR_SERVICE";
    public static final String PUBLIC_SECTOR_SERVICE_CODE = "02";
    public static final String PUBLIC_SECTOR_SERVICE_DESC = "Public Sector Service";

    /**
     * Business
     */
    public static final String BUSINESS = "BUSINESS";
    public static final String BUSINESS_CODE = "03";
    public static final String BUSINESS_DESC = "Business";

    /**
     * Professional
     */
    public static final String PROFESSIONAL = "PROFESSIONAL";
    public static final String PROFESSIONAL_CODE = "04";
    public static final String PROFESSIONAL_DESC = "Professional";

    /**
     * Agriculturist
     */
    public static final String AGRICULTURIST = "AGRICULTURIST";
    public static final String AGRICULTURIST_CODE = "05";
    public static final String AGRICULTURIST_DESC = "Agriculturist";

    /**
     * Retired
     */
    public static final String RETIRED = "RETIRED";
    public static final String RETIRED_CODE = "06";
    public static final String RETIRED_DESC = "Retired";

    /**
     * Housewife
     */
    public static final String HOUSEWIFE = "HOUSEWIFE";
    public static final String HOUSEWIFE_CODE = "07";
    public static final String HOUSEWIFE_DESC = "Housewife";

    /**
     * Student
     */
    public static final String STUDENT = "STUDENT";
    public static final String STUDENT_CODE = "08";
    public static final String STUDENT_DESC = "Student";

    /**
     * Forex Dealer
     */
    public static final String FOREX_DEALER = "FOREX_DEALER";
    public static final String FOREX_DEALER_CODE = "09";
    public static final String FOREX_DEALER_DESC = "Forex Dealer";

    /**
     * Government Service
     */
    public static final String GOVERNMENT_SERVICE = "GOVERNMENT_SERVICE";
    public static final String GOVERNMENT_SERVICE_CODE = "10";
    public static final String GOVERNMENT_SERVICE_DESC = "Government Service";

    /**
     * Others
     */
    public static final String OTHERS = "OTHERS";
    public static final String OTHERS_CODE = "99";
    public static final String OTHERS_DESC = "Others";

    /**
     * List of all occupation codes
     */
    public static final List<String> OCCUPATION_CODES = Arrays.asList(
            PRIVATE_SECTOR_SERVICE_CODE, PUBLIC_SECTOR_SERVICE_CODE, BUSINESS_CODE, PROFESSIONAL_CODE,
            AGRICULTURIST_CODE, RETIRED_CODE, HOUSEWIFE_CODE, STUDENT_CODE, FOREX_DEALER_CODE,
            GOVERNMENT_SERVICE_CODE, OTHERS_CODE);

    /**
     * List of occupation codes as enum
     */
    public static final List<OccupationType> OCCUPATION_CODES_AS_ENUM = Arrays.asList(
            OccupationType.PRIVATE_SECTOR_SERVICE,
            OccupationType.PUBLIC_SECTOR_SERVICE,
            OccupationType.BUSINESS,
            OccupationType.PROFESSIONAL,
            OccupationType.AGRICULTURIST,
            OccupationType.RETIRED,
            OccupationType.HOUSEWIFE,
            OccupationType.STUDENT,
            OccupationType.FOREX_DEALER,
            OccupationType.GOVERNMENT_SERVICE,
            OccupationType.OTHERS);

    /**
     * Occupation Type enum
     */
    public enum OccupationType {
        PRIVATE_SECTOR_SERVICE("Private Sector Service"),
        PUBLIC_SECTOR_SERVICE("Public Sector Service"),
        BUSINESS("Business"),
        PROFESSIONAL("Professional"),
        AGRICULTURIST("Agriculturist"),
        RETIRED("Retired"),
        HOUSEWIFE("Housewife"),
        STUDENT("Student"),
        FOREX_DEALER("Forex Dealer"),
        GOVERNMENT_SERVICE("Government Service"),
        OTHERS("Others");

        private final String description;

        OccupationType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case PRIVATE_SECTOR_SERVICE:
                    return PRIVATE_SECTOR_SERVICE_CODE;
                case PUBLIC_SECTOR_SERVICE:
                    return PUBLIC_SECTOR_SERVICE_CODE;
                case BUSINESS:
                    return BUSINESS_CODE;
                case PROFESSIONAL:
                    return PROFESSIONAL_CODE;
                case AGRICULTURIST:
                    return AGRICULTURIST_CODE;
                case RETIRED:
                    return RETIRED_CODE;
                case HOUSEWIFE:
                    return HOUSEWIFE_CODE;
                case STUDENT:
                    return STUDENT_CODE;
                case FOREX_DEALER:
                    return FOREX_DEALER_CODE;
                case GOVERNMENT_SERVICE:
                    return GOVERNMENT_SERVICE_CODE;
                case OTHERS:
                    return OTHERS_CODE;
                default:
                    throw new IllegalStateException("Unknown occupation type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the occupation type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<OccupationType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case PRIVATE_SECTOR_SERVICE_CODE:
                    return Optional.of(OccupationType.PRIVATE_SECTOR_SERVICE);
                case PUBLIC_SECTOR_SERVICE_CODE:
                    return Optional.of(OccupationType.PUBLIC_SECTOR_SERVICE);
                case BUSINESS_CODE:
                    return Optional.of(OccupationType.BUSINESS);
                case PROFESSIONAL_CODE:
                    return Optional.of(OccupationType.PROFESSIONAL);
                case AGRICULTURIST_CODE:
                    return Optional.of(OccupationType.AGRICULTURIST);
                case RETIRED_CODE:
                    return Optional.of(OccupationType.RETIRED);
                case HOUSEWIFE_CODE:
                    return Optional.of(OccupationType.HOUSEWIFE);
                case STUDENT_CODE:
                    return Optional.of(OccupationType.STUDENT);
                case FOREX_DEALER_CODE:
                    return Optional.of(OccupationType.FOREX_DEALER);
                case GOVERNMENT_SERVICE_CODE:
                    return Optional.of(OccupationType.GOVERNMENT_SERVICE);
                case OTHERS_CODE:
                    return Optional.of(OccupationType.OTHERS);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse occupation type from string
         *
         * @param value the string value
         * @return the occupation type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static OccupationType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case Occupation.PRIVATE_SECTOR_SERVICE:
                    return OccupationType.PRIVATE_SECTOR_SERVICE;
                case Occupation.PUBLIC_SECTOR_SERVICE:
                    return OccupationType.PUBLIC_SECTOR_SERVICE;
                case Occupation.BUSINESS:
                    return OccupationType.BUSINESS;
                case Occupation.PROFESSIONAL:
                    return OccupationType.PROFESSIONAL;
                case Occupation.AGRICULTURIST:
                    return OccupationType.AGRICULTURIST;
                case Occupation.RETIRED:
                    return OccupationType.RETIRED;
                case Occupation.HOUSEWIFE:
                    return OccupationType.HOUSEWIFE;
                case Occupation.STUDENT:
                    return OccupationType.STUDENT;
                case Occupation.FOREX_DEALER:
                    return OccupationType.FOREX_DEALER;
                case Occupation.GOVERNMENT_SERVICE:
                    return OccupationType.GOVERNMENT_SERVICE;
                case Occupation.OTHERS:
                    return OccupationType.OTHERS;
                default:
                    throw new IllegalArgumentException("Invalid occupation: " + value);
            }
        }
    }
}
