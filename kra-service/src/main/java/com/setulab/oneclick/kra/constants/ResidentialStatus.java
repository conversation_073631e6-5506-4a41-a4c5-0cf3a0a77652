package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Residential Status constants and enum
 */
public class ResidentialStatus {

    // Constants
    /**
     * Resident Individual
     */
    public static final String RESIDENT_INDIVIDUAL = "RESIDENT_INDIVIDUAL";
    public static final String RESIDENT_INDIVIDUAL_CODE = "R";
    public static final String RESIDENT_INDIVIDUAL_DESC = "Resident Individual";

    /**
     * Non-Resident Individual
     */
    public static final String NON_RESIDENT_INDIVIDUAL = "NON_RESIDENT_INDIVIDUAL";
    public static final String NON_RESIDENT_INDIVIDUAL_CODE = "N";
    public static final String NON_RESIDENT_INDIVIDUAL_DESC = "Non-Resident Individual";

    /**
     * Foreign National
     */
    public static final String FOREIGN_NATIONAL = "FOREIGN_NATIONAL";
    public static final String FOREIGN_NATIONAL_CODE = "P";
    public static final String FOREIGN_NATIONAL_DESC = "Foreign National";

    /**
     * QFI
     */
    public static final String QFI = "QFI";
    public static final String QFI_CODE = "Q";
    public static final String QFI_DESC = "QFI";

    /**
     * Eligible Foreign Investor Category III
     */
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III = "ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE = "3";
    public static final String ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_DESC = "Eligible Foreign Investor Category III";

    /**
     * List of all residential status codes
     */
    public static final List<String> RESIDENTIAL_STATUS_CODES = Arrays.asList(
            RESIDENT_INDIVIDUAL_CODE, NON_RESIDENT_INDIVIDUAL_CODE, FOREIGN_NATIONAL_CODE,
            QFI_CODE, ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE);

    /**
     * List of residential status codes as enum
     */
    public static final List<ResidentialStatusType> RESIDENTIAL_STATUS_CODES_AS_ENUM = Arrays.asList(
            ResidentialStatusType.RESIDENT_INDIVIDUAL,
            ResidentialStatusType.NON_RESIDENT_INDIVIDUAL,
            ResidentialStatusType.FOREIGN_NATIONAL,
            ResidentialStatusType.QFI,
            ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III);

    /**
     * Residential Status Type enum
     */
    public enum ResidentialStatusType {
        RESIDENT_INDIVIDUAL("Resident Individual"),
        NON_RESIDENT_INDIVIDUAL("Non-Resident Individual"),
        FOREIGN_NATIONAL("Foreign National"),
        QFI("QFI"),
        ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III("Eligible Foreign Investor Category III");

        private final String description;

        ResidentialStatusType(String description) {
            this.description = description;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case RESIDENT_INDIVIDUAL:
                    return RESIDENT_INDIVIDUAL_CODE;
                case NON_RESIDENT_INDIVIDUAL:
                    return NON_RESIDENT_INDIVIDUAL_CODE;
                case FOREIGN_NATIONAL:
                    return FOREIGN_NATIONAL_CODE;
                case QFI:
                    return QFI_CODE;
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III:
                    return ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE;
                default:
                    throw new IllegalStateException("Unknown residential status type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the residential status type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<ResidentialStatusType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case RESIDENT_INDIVIDUAL_CODE:
                    return Optional.of(ResidentialStatusType.RESIDENT_INDIVIDUAL);
                case NON_RESIDENT_INDIVIDUAL_CODE:
                    return Optional.of(ResidentialStatusType.NON_RESIDENT_INDIVIDUAL);
                case FOREIGN_NATIONAL_CODE:
                    return Optional.of(ResidentialStatusType.FOREIGN_NATIONAL);
                case QFI_CODE:
                    return Optional.of(ResidentialStatusType.QFI);
                case ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE:
                    return Optional.of(ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse residential status type from string
         *
         * @param value the string value
         * @return the residential status type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static ResidentialStatusType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case ResidentialStatus.RESIDENT_INDIVIDUAL:
                    return ResidentialStatusType.RESIDENT_INDIVIDUAL;
                case ResidentialStatus.NON_RESIDENT_INDIVIDUAL:
                    return ResidentialStatusType.NON_RESIDENT_INDIVIDUAL;
                case ResidentialStatus.FOREIGN_NATIONAL:
                    return ResidentialStatusType.FOREIGN_NATIONAL;
                case ResidentialStatus.QFI:
                    return ResidentialStatusType.QFI;
                case ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III:
                    return ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III;
                default:
                    throw new IllegalArgumentException("Invalid residential status: " + value);
            }
        }
    }
}
