package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * State Master constants and enum for Indian States and Union Territories
 */
public class StateMaster {

    // Northern States
    /**
     * Jammu and Kashmir
     */
    public static final String JAMMU_AND_KASHMIR = "JAMMU_AND_KASHMIR";
    public static final String JAMMU_AND_KASHMIR_CODE = "001";
    public static final String JAMMU_AND_KASHMIR_DESC = "Jammu and Kashmir";

    /**
     * Himachal Pradesh
     */
    public static final String HIMACHAL_PRADESH = "HIMACHAL_PRADESH";
    public static final String HIMACHAL_PRADESH_CODE = "002";
    public static final String HIMACHAL_PRADESH_DESC = "Himachal Pradesh";

    /**
     * Punjab
     */
    public static final String PUNJAB = "PUNJAB";
    public static final String PUNJAB_CODE = "003";
    public static final String PUNJAB_DESC = "Punjab";

    /**
     * Chandigarh
     */
    public static final String CHANDIGARH = "CHANDIGARH";
    public static final String CHANDIGARH_CODE = "004";
    public static final String CHANDIGARH_DESC = "Chandigarh";

    /**
     * Uttarakhand
     */
    public static final String UTTARAKHAND = "UTTARAKHAND";
    public static final String UTTARAKHAND_CODE = "005";
    public static final String UTTARAKHAND_DESC = "Uttarakhand";

    /**
     * Haryana
     */
    public static final String HARYANA = "HARYANA";
    public static final String HARYANA_CODE = "006";
    public static final String HARYANA_DESC = "Haryana";

    /**
     * Delhi
     */
    public static final String DELHI = "DELHI";
    public static final String DELHI_CODE = "007";
    public static final String DELHI_DESC = "Delhi";

    /**
     * Rajasthan
     */
    public static final String RAJASTHAN = "RAJASTHAN";
    public static final String RAJASTHAN_CODE = "008";
    public static final String RAJASTHAN_DESC = "Rajasthan";

    /**
     * Uttar Pradesh
     */
    public static final String UTTAR_PRADESH = "UTTAR_PRADESH";
    public static final String UTTAR_PRADESH_CODE = "009";
    public static final String UTTAR_PRADESH_DESC = "Uttar Pradesh";

    /**
     * Bihar
     */
    public static final String BIHAR = "BIHAR";
    public static final String BIHAR_CODE = "010";
    public static final String BIHAR_DESC = "Bihar";

    // Northeastern States
    /**
     * Sikkim
     */
    public static final String SIKKIM = "SIKKIM";
    public static final String SIKKIM_CODE = "011";
    public static final String SIKKIM_DESC = "Sikkim";

    /**
     * Arunachal Pradesh
     */
    public static final String ARUNACHAL_PRADESH = "ARUNACHAL_PRADESH";
    public static final String ARUNACHAL_PRADESH_CODE = "012";
    public static final String ARUNACHAL_PRADESH_DESC = "Arunachal Pradesh";

    /**
     * Assam
     */
    public static final String ASSAM = "ASSAM";
    public static final String ASSAM_CODE = "013";
    public static final String ASSAM_DESC = "Assam";

    /**
     * Manipur
     */
    public static final String MANIPUR = "MANIPUR";
    public static final String MANIPUR_CODE = "014";
    public static final String MANIPUR_DESC = "Manipur";

    /**
     * Mizoram
     */
    public static final String MIZORAM = "MIZORAM";
    public static final String MIZORAM_CODE = "015";
    public static final String MIZORAM_DESC = "Mizoram";

    /**
     * Tripura
     */
    public static final String TRIPURA = "TRIPURA";
    public static final String TRIPURA_CODE = "016";
    public static final String TRIPURA_DESC = "Tripura";

    /**
     * Meghalaya
     */
    public static final String MEGHALAYA = "MEGHALAYA";
    public static final String MEGHALAYA_CODE = "017";
    public static final String MEGHALAYA_DESC = "Meghalaya";

    /**
     * Nagaland
     */
    public static final String NAGALAND = "NAGALAND";
    public static final String NAGALAND_CODE = "018";
    public static final String NAGALAND_DESC = "Nagaland";

    // Eastern States
    /**
     * West Bengal
     */
    public static final String WEST_BENGAL = "WEST_BENGAL";
    public static final String WEST_BENGAL_CODE = "019";
    public static final String WEST_BENGAL_DESC = "West Bengal";

    /**
     * Jharkhand
     */
    public static final String JHARKHAND = "JHARKHAND";
    public static final String JHARKHAND_CODE = "020";
    public static final String JHARKHAND_DESC = "Jharkhand";

    /**
     * Orissa
     */
    public static final String ORISSA = "ORISSA";
    public static final String ORISSA_CODE = "021";
    public static final String ORISSA_DESC = "Orissa";

    // Central States
    /**
     * Chhattisgarh
     */
    public static final String CHHATTISGARH = "CHHATTISGARH";
    public static final String CHHATTISGARH_CODE = "022";
    public static final String CHHATTISGARH_DESC = "Chhattisgarh";

    /**
     * Madhya Pradesh
     */
    public static final String MADHYA_PRADESH = "MADHYA_PRADESH";
    public static final String MADHYA_PRADESH_CODE = "023";
    public static final String MADHYA_PRADESH_DESC = "Madhya Pradesh";

    // Western States
    /**
     * Gujarat
     */
    public static final String GUJARAT = "GUJARAT";
    public static final String GUJARAT_CODE = "024";
    public static final String GUJARAT_DESC = "Gujarat";

    /**
     * Daman and Diu
     */
    public static final String DAMAN_AND_DIU = "DAMAN_AND_DIU";
    public static final String DAMAN_AND_DIU_CODE = "025";
    public static final String DAMAN_AND_DIU_DESC = "Daman and Diu";

    /**
     * Dadra and Nagar Haveli
     */
    public static final String DADRA_AND_NAGAR_HAVELI = "DADRA_AND_NAGAR_HAVELI";
    public static final String DADRA_AND_NAGAR_HAVELI_CODE = "026";
    public static final String DADRA_AND_NAGAR_HAVELI_DESC = "Dadra and Nagar Haveli";

    /**
     * Maharashtra
     */
    public static final String MAHARASHTRA = "MAHARASHTRA";
    public static final String MAHARASHTRA_CODE = "027";
    public static final String MAHARASHTRA_DESC = "Maharashtra";

    // Southern States
    /**
     * Andhra Pradesh
     */
    public static final String ANDHRA_PRADESH = "ANDHRA_PRADESH";
    public static final String ANDHRA_PRADESH_CODE = "028";
    public static final String ANDHRA_PRADESH_DESC = "Andhra Pradesh";

    /**
     * Karnataka
     */
    public static final String KARNATAKA = "KARNATAKA";
    public static final String KARNATAKA_CODE = "029";
    public static final String KARNATAKA_DESC = "Karnataka";

    /**
     * Goa
     */
    public static final String GOA = "GOA";
    public static final String GOA_CODE = "030";
    public static final String GOA_DESC = "Goa";

    /**
     * Lakshadweep
     */
    public static final String LAKSHADWEEP = "LAKSHADWEEP";
    public static final String LAKSHADWEEP_CODE = "031";
    public static final String LAKSHADWEEP_DESC = "Lakshadweep";

    /**
     * Kerala
     */
    public static final String KERALA = "KERALA";
    public static final String KERALA_CODE = "032";
    public static final String KERALA_DESC = "Kerala";

    /**
     * Tamil Nadu
     */
    public static final String TAMIL_NADU = "TAMIL_NADU";
    public static final String TAMIL_NADU_CODE = "033";
    public static final String TAMIL_NADU_DESC = "Tamil Nadu";

    /**
     * Puducherry
     */
    public static final String PUDUCHERRY = "PUDUCHERRY";
    public static final String PUDUCHERRY_CODE = "034";
    public static final String PUDUCHERRY_DESC = "Puducherry";

    /**
     * Andaman and Nicobar Islands
     */
    public static final String ANDAMAN_AND_NICOBAR_ISLANDS = "ANDAMAN_AND_NICOBAR_ISLANDS";
    public static final String ANDAMAN_AND_NICOBAR_ISLANDS_CODE = "035";
    public static final String ANDAMAN_AND_NICOBAR_ISLANDS_DESC = "Andaman and Nicobar Islands";

    /**
     * APO
     */
    public static final String APO = "APO";
    public static final String APO_CODE = "036";
    public static final String APO_DESC = "APO";

    /**
     * Telangana
     */
    public static final String TELANGANA = "TELANGANA";
    public static final String TELANGANA_CODE = "037";
    public static final String TELANGANA_DESC = "Telangana";

    /**
     * Ladakh
     */
    public static final String LADAKH = "LADAKH";
    public static final String LADAKH_CODE = "038";
    public static final String LADAKH_DESC = "Ladakh";

    /**
     * Other
     */
    public static final String OTHER = "OTHER";
    public static final String OTHER_CODE = "039";
    public static final String OTHER_DESC = "Other";

    /**
     * List of all state codes
     */
    public static final List<String> STATE_CODES = Arrays.asList(
            JAMMU_AND_KASHMIR_CODE, HIMACHAL_PRADESH_CODE, PUNJAB_CODE, CHANDIGARH_CODE,
            UTTARAKHAND_CODE, HARYANA_CODE, DELHI_CODE, RAJASTHAN_CODE, UTTAR_PRADESH_CODE,
            BIHAR_CODE, SIKKIM_CODE, ARUNACHAL_PRADESH_CODE, ASSAM_CODE, MANIPUR_CODE,
            MIZORAM_CODE, TRIPURA_CODE, MEGHALAYA_CODE, NAGALAND_CODE, WEST_BENGAL_CODE,
            JHARKHAND_CODE, ORISSA_CODE, CHHATTISGARH_CODE, MADHYA_PRADESH_CODE, GUJARAT_CODE,
            DAMAN_AND_DIU_CODE, DADRA_AND_NAGAR_HAVELI_CODE, MAHARASHTRA_CODE, ANDHRA_PRADESH_CODE,
            KARNATAKA_CODE, GOA_CODE, LAKSHADWEEP_CODE, KERALA_CODE, TAMIL_NADU_CODE,
            PUDUCHERRY_CODE, ANDAMAN_AND_NICOBAR_ISLANDS_CODE, APO_CODE, TELANGANA_CODE,
            LADAKH_CODE, OTHER_CODE);

    /**
     * State Master Type enum
     */
    public enum StateMasterType {
        // Northern States
        JAMMU_AND_KASHMIR("Jammu and Kashmir"),
        HIMACHAL_PRADESH("Himachal Pradesh"),
        PUNJAB("Punjab"),
        CHANDIGARH("Chandigarh"),
        UTTARAKHAND("Uttarakhand"),
        HARYANA("Haryana"),
        DELHI("Delhi"),
        RAJASTHAN("Rajasthan"),
        UTTAR_PRADESH("Uttar Pradesh"),
        BIHAR("Bihar"),

        // Northeastern States
        SIKKIM("Sikkim"),
        ARUNACHAL_PRADESH("Arunachal Pradesh"),
        ASSAM("Assam"),
        MANIPUR("Manipur"),
        MIZORAM("Mizoram"),
        TRIPURA("Tripura"),
        MEGHALAYA("Meghalaya"),
        NAGALAND("Nagaland"),

        // Eastern States
        WEST_BENGAL("West Bengal"),
        JHARKHAND("Jharkhand"),
        ORISSA("Orissa"),

        // Central States
        CHHATTISGARH("Chhattisgarh"),
        MADHYA_PRADESH("Madhya Pradesh"),

        // Western States
        GUJARAT("Gujarat"),
        DAMAN_AND_DIU("Daman and Diu"),
        DADRA_AND_NAGAR_HAVELI("Dadra and Nagar Haveli"),
        MAHARASHTRA("Maharashtra"),

        // Southern States
        ANDHRA_PRADESH("Andhra Pradesh"),
        KARNATAKA("Karnataka"),
        GOA("Goa"),
        LAKSHADWEEP("Lakshadweep"),
        KERALA("Kerala"),
        TAMIL_NADU("Tamil Nadu"),
        PUDUCHERRY("Puducherry"),
        ANDAMAN_AND_NICOBAR_ISLANDS("Andaman and Nicobar Islands"),

        // Special
        APO("APO"),
        TELANGANA("Telangana"),
        LADAKH("Ladakh"),
        OTHER("Other");

        private final String description;

        StateMasterType(String description) {
            this.description = description;
        }

        /**
         * Get the description of this state
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the code representation of this state
         *
         * @return the code string
         */
        public String asCode() {
            // Due to size constraints, implementing key states only
            switch (this) {
                case JAMMU_AND_KASHMIR:
                    return JAMMU_AND_KASHMIR_CODE;
                case HIMACHAL_PRADESH:
                    return HIMACHAL_PRADESH_CODE;
                case PUNJAB:
                    return PUNJAB_CODE;
                case CHANDIGARH:
                    return CHANDIGARH_CODE;
                case UTTARAKHAND:
                    return UTTARAKHAND_CODE;
                case HARYANA:
                    return HARYANA_CODE;
                case DELHI:
                    return DELHI_CODE;
                case RAJASTHAN:
                    return RAJASTHAN_CODE;
                case UTTAR_PRADESH:
                    return UTTAR_PRADESH_CODE;
                case BIHAR:
                    return BIHAR_CODE;
                case SIKKIM:
                    return SIKKIM_CODE;
                case ARUNACHAL_PRADESH:
                    return ARUNACHAL_PRADESH_CODE;
                case ASSAM:
                    return ASSAM_CODE;
                case MANIPUR:
                    return MANIPUR_CODE;
                case MIZORAM:
                    return MIZORAM_CODE;
                case TRIPURA:
                    return TRIPURA_CODE;
                case MEGHALAYA:
                    return MEGHALAYA_CODE;
                case NAGALAND:
                    return NAGALAND_CODE;
                case WEST_BENGAL:
                    return WEST_BENGAL_CODE;
                case JHARKHAND:
                    return JHARKHAND_CODE;
                case ORISSA:
                    return ORISSA_CODE;
                case CHHATTISGARH:
                    return CHHATTISGARH_CODE;
                case MADHYA_PRADESH:
                    return MADHYA_PRADESH_CODE;
                case GUJARAT:
                    return GUJARAT_CODE;
                case DAMAN_AND_DIU:
                    return DAMAN_AND_DIU_CODE;
                case DADRA_AND_NAGAR_HAVELI:
                    return DADRA_AND_NAGAR_HAVELI_CODE;
                case MAHARASHTRA:
                    return MAHARASHTRA_CODE;
                case ANDHRA_PRADESH:
                    return ANDHRA_PRADESH_CODE;
                case KARNATAKA:
                    return KARNATAKA_CODE;
                case GOA:
                    return GOA_CODE;
                case LAKSHADWEEP:
                    return LAKSHADWEEP_CODE;
                case KERALA:
                    return KERALA_CODE;
                case TAMIL_NADU:
                    return TAMIL_NADU_CODE;
                case PUDUCHERRY:
                    return PUDUCHERRY_CODE;
                case ANDAMAN_AND_NICOBAR_ISLANDS:
                    return ANDAMAN_AND_NICOBAR_ISLANDS_CODE;
                case APO:
                    return APO_CODE;
                case TELANGANA:
                    return TELANGANA_CODE;
                case LADAKH:
                    return LADAKH_CODE;
                case OTHER:
                    return OTHER_CODE;
                default:
                    throw new IllegalStateException("Unknown state type: " + this);
            }
        }
    }
}
