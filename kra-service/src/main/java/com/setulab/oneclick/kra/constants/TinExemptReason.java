package com.setulab.oneclick.kra.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * TIN Exempt Reason constants and enum
 */
public class TinExemptReason {

    // Individual reasons
    /**
     * Student
     */
    public static final String STUDENT = "STUDENT";
    public static final String STUDENT_CODE = "01";
    public static final String STUDENT_DESC = "Student";
    public static final String STUDENT_TYPE = "Individual";

    /**
     * Home-maker
     */
    public static final String HOME_MAKER = "HOME_MAKER";
    public static final String HOME_MAKER_CODE = "02";
    public static final String HOME_MAKER_DESC = "Home-maker";
    public static final String HOME_MAKER_TYPE = "Individual";

    /**
     * House-Wife
     */
    public static final String HOUSE_WIFE = "HOUSE_WIFE";
    public static final String HOUSE_WIFE_CODE = "03";
    public static final String HOUSE_WIFE_DESC = "House-Wife";
    public static final String HOUSE_WIFE_TYPE = "Individual";

    /**
     * Retired
     */
    public static final String RETIRED = "RETIRED";
    public static final String RETIRED_CODE = "04";
    public static final String RETIRED_DESC = "Retired";
    public static final String RETIRED_TYPE = "Individual";

    /**
     * Diplomat
     */
    public static final String DIPLOMAT = "DIPLOMAT";
    public static final String DIPLOMAT_CODE = "05";
    public static final String DIPLOMAT_DESC = "Diplomat";
    public static final String DIPLOMAT_TYPE = "Individual";

    /**
     * TIN Not Eligible as of now NA
     */
    public static final String TIN_NOT_ELIGIBLE = "TIN_NOT_ELIGIBLE";
    public static final String TIN_NOT_ELIGIBLE_CODE = "06";
    public static final String TIN_NOT_ELIGIBLE_DESC = "TIN Not Eligible as of now NA.";
    public static final String TIN_NOT_ELIGIBLE_TYPE = "Individual";

    // Non-Individual reasons
    /**
     * Entity's Country of Incorporation / Tax Residence is US but Entity is not a Specified US person
     */
    public static final String US_ENTITY_NOT_SPECIFIED_US_PERSON = "US_ENTITY_NOT_SPECIFIED_US_PERSON";
    public static final String US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE = "07";
    public static final String US_ENTITY_NOT_SPECIFIED_US_PERSON_DESC = "Entitys Country of Incorporation / Tax Residence is US but Entity is not a Specified US person";
    public static final String US_ENTITY_NOT_SPECIFIED_US_PERSON_TYPE = "Non-Individual";

    /**
     * FFI - GIIN Application Pending
     */
    public static final String FFI_GIIN_APPLICATION_PENDING = "FFI_GIIN_APPLICATION_PENDING";
    public static final String FFI_GIIN_APPLICATION_PENDING_CODE = "08";
    public static final String FFI_GIIN_APPLICATION_PENDING_DESC = "FFI - GIIN Application Pending";
    public static final String FFI_GIIN_APPLICATION_PENDING_TYPE = "Non-Individual";

    /**
     * NFFE - GIIN Application Pending
     */
    public static final String NFFE_GIIN_APPLICATION_PENDING = "NFFE_GIIN_APPLICATION_PENDING";
    public static final String NFFE_GIIN_APPLICATION_PENDING_CODE = "09";
    public static final String NFFE_GIIN_APPLICATION_PENDING_DESC = "NFFE - GIIN Application Pending";
    public static final String NFFE_GIIN_APPLICATION_PENDING_TYPE = "Non-Individual";

    /**
     * FFI - GIIN Not Required
     */
    public static final String FFI_GIIN_NOT_REQUIRED = "FFI_GIIN_NOT_REQUIRED";
    public static final String FFI_GIIN_NOT_REQUIRED_CODE = "10";
    public static final String FFI_GIIN_NOT_REQUIRED_DESC = "FFI - GIIN Not Required";
    public static final String FFI_GIIN_NOT_REQUIRED_TYPE = "Non-Individual";

    /**
     * NFFE - GIIN Not Required
     */
    public static final String NFFE_GIIN_NOT_REQUIRED = "NFFE_GIIN_NOT_REQUIRED";
    public static final String NFFE_GIIN_NOT_REQUIRED_CODE = "11";
    public static final String NFFE_GIIN_NOT_REQUIRED_DESC = "NFFE - GIIN Not Required";
    public static final String NFFE_GIIN_NOT_REQUIRED_TYPE = "Non-Individual";

    /**
     * Non-Participating FFI
     */
    public static final String NON_PARTICIPATING_FFI = "NON_PARTICIPATING_FFI";
    public static final String NON_PARTICIPATING_FFI_CODE = "12";
    public static final String NON_PARTICIPATING_FFI_DESC = "Non-Participating FFI";
    public static final String NON_PARTICIPATING_FFI_TYPE = "Non-Individual";

    /**
     * FATCA Exempt NFFE
     */
    public static final String FATCA_EXEMPT_NFFE = "FATCA_EXEMPT_NFFE";
    public static final String FATCA_EXEMPT_NFFE_CODE = "13";
    public static final String FATCA_EXEMPT_NFFE_DESC = "FATCA Exempt NFFE";
    public static final String FATCA_EXEMPT_NFFE_TYPE = "Non-Individual";

    /**
     * Others
     */
    public static final String OTHERS = "OTHERS";
    public static final String OTHERS_CODE = "14";
    public static final String OTHERS_DESC = "Others";
    public static final String OTHERS_TYPE = "Non-Individual";

    /**
     * List of all TIN exempt reason codes
     */
    public static final List<String> TIN_EXEMPT_REASON_CODES = Arrays.asList(
            STUDENT_CODE, HOME_MAKER_CODE, HOUSE_WIFE_CODE, RETIRED_CODE, DIPLOMAT_CODE,
            TIN_NOT_ELIGIBLE_CODE, US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE, FFI_GIIN_APPLICATION_PENDING_CODE,
            NFFE_GIIN_APPLICATION_PENDING_CODE, FFI_GIIN_NOT_REQUIRED_CODE, NFFE_GIIN_NOT_REQUIRED_CODE,
            NON_PARTICIPATING_FFI_CODE, FATCA_EXEMPT_NFFE_CODE, OTHERS_CODE);

    /**
     * List of TIN exempt reason codes as enum
     */
    public static final List<TinExemptReasonType> TIN_EXEMPT_REASON_CODES_AS_ENUM = Arrays.asList(
            TinExemptReasonType.STUDENT,
            TinExemptReasonType.HOME_MAKER,
            TinExemptReasonType.HOUSE_WIFE,
            TinExemptReasonType.RETIRED,
            TinExemptReasonType.DIPLOMAT,
            TinExemptReasonType.TIN_NOT_ELIGIBLE,
            TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON,
            TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING,
            TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING,
            TinExemptReasonType.FFI_GIIN_NOT_REQUIRED,
            TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED,
            TinExemptReasonType.NON_PARTICIPATING_FFI,
            TinExemptReasonType.FATCA_EXEMPT_NFFE,
            TinExemptReasonType.OTHERS);

    /**
     * TIN Exempt Reason Type enum
     */
    public enum TinExemptReasonType {
        STUDENT("Student", "Individual"),
        HOME_MAKER("Home-maker", "Individual"),
        HOUSE_WIFE("House-Wife", "Individual"),
        RETIRED("Retired", "Individual"),
        DIPLOMAT("Diplomat", "Individual"),
        TIN_NOT_ELIGIBLE("TIN Not Eligible as of now NA.", "Individual"),
        US_ENTITY_NOT_SPECIFIED_US_PERSON("Entitys Country of Incorporation / Tax Residence is US but Entity is not a Specified US person", "Non-Individual"),
        FFI_GIIN_APPLICATION_PENDING("FFI - GIIN Application Pending", "Non-Individual"),
        NFFE_GIIN_APPLICATION_PENDING("NFFE - GIIN Application Pending", "Non-Individual"),
        FFI_GIIN_NOT_REQUIRED("FFI - GIIN Not Required", "Non-Individual"),
        NFFE_GIIN_NOT_REQUIRED("NFFE - GIIN Not Required", "Non-Individual"),
        NON_PARTICIPATING_FFI("Non-Participating FFI", "Non-Individual"),
        FATCA_EXEMPT_NFFE("FATCA Exempt NFFE", "Non-Individual"),
        OTHERS("Others", "Non-Individual");

        private final String description;
        private final String type;

        TinExemptReasonType(String description, String type) {
            this.description = description;
            this.type = type;
        }

        /**
         * Get the code representation of this type
         *
         * @return the code string
         */
        public String asCode() {
            switch (this) {
                case STUDENT:
                    return STUDENT_CODE;
                case HOME_MAKER:
                    return HOME_MAKER_CODE;
                case HOUSE_WIFE:
                    return HOUSE_WIFE_CODE;
                case RETIRED:
                    return RETIRED_CODE;
                case DIPLOMAT:
                    return DIPLOMAT_CODE;
                case TIN_NOT_ELIGIBLE:
                    return TIN_NOT_ELIGIBLE_CODE;
                case US_ENTITY_NOT_SPECIFIED_US_PERSON:
                    return US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE;
                case FFI_GIIN_APPLICATION_PENDING:
                    return FFI_GIIN_APPLICATION_PENDING_CODE;
                case NFFE_GIIN_APPLICATION_PENDING:
                    return NFFE_GIIN_APPLICATION_PENDING_CODE;
                case FFI_GIIN_NOT_REQUIRED:
                    return FFI_GIIN_NOT_REQUIRED_CODE;
                case NFFE_GIIN_NOT_REQUIRED:
                    return NFFE_GIIN_NOT_REQUIRED_CODE;
                case NON_PARTICIPATING_FFI:
                    return NON_PARTICIPATING_FFI_CODE;
                case FATCA_EXEMPT_NFFE:
                    return FATCA_EXEMPT_NFFE_CODE;
                case OTHERS:
                    return OTHERS_CODE;
                default:
                    throw new IllegalStateException("Unknown TIN exempt reason type: " + this);
            }
        }

        /**
         * Get the description of this type
         *
         * @return the description string
         */
        public String asDescription() {
            return description;
        }

        /**
         * Get the type (Individual/Non-Individual) of this reason
         *
         * @return the type string
         */
        public String asType() {
            return type;
        }

        /**
         * Check if the TIN exempt reason is for individuals
         *
         * @return true if for individuals
         */
        public boolean isForIndividuals() {
            return "Individual".equals(type);
        }

        /**
         * Check if the TIN exempt reason is for non-individuals
         *
         * @return true if for non-individuals
         */
        public boolean isForNonIndividuals() {
            return "Non-Individual".equals(type);
        }

        /**
         * Get the TIN exempt reason type from the code
         *
         * @param code the code string
         * @return Optional containing the type if found, empty otherwise
         */
        public static Optional<TinExemptReasonType> fromCode(String code) {
            if (code == null) {
                return Optional.empty();
            }

            switch (code) {
                case STUDENT_CODE:
                    return Optional.of(TinExemptReasonType.STUDENT);
                case HOME_MAKER_CODE:
                    return Optional.of(TinExemptReasonType.HOME_MAKER);
                case HOUSE_WIFE_CODE:
                    return Optional.of(TinExemptReasonType.HOUSE_WIFE);
                case RETIRED_CODE:
                    return Optional.of(TinExemptReasonType.RETIRED);
                case DIPLOMAT_CODE:
                    return Optional.of(TinExemptReasonType.DIPLOMAT);
                case TIN_NOT_ELIGIBLE_CODE:
                    return Optional.of(TinExemptReasonType.TIN_NOT_ELIGIBLE);
                case US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE:
                    return Optional.of(TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON);
                case FFI_GIIN_APPLICATION_PENDING_CODE:
                    return Optional.of(TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING);
                case NFFE_GIIN_APPLICATION_PENDING_CODE:
                    return Optional.of(TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING);
                case FFI_GIIN_NOT_REQUIRED_CODE:
                    return Optional.of(TinExemptReasonType.FFI_GIIN_NOT_REQUIRED);
                case NFFE_GIIN_NOT_REQUIRED_CODE:
                    return Optional.of(TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED);
                case NON_PARTICIPATING_FFI_CODE:
                    return Optional.of(TinExemptReasonType.NON_PARTICIPATING_FFI);
                case FATCA_EXEMPT_NFFE_CODE:
                    return Optional.of(TinExemptReasonType.FATCA_EXEMPT_NFFE);
                case OTHERS_CODE:
                    return Optional.of(TinExemptReasonType.OTHERS);
                default:
                    return Optional.empty();
            }
        }

        /**
         * Parse TIN exempt reason type from string
         *
         * @param value the string value
         * @return the TIN exempt reason type
         * @throws IllegalArgumentException if the value is invalid
         */
        public static TinExemptReasonType fromString(String value) {
            if (value == null) {
                throw new IllegalArgumentException("Value cannot be null");
            }

            switch (value) {
                case TinExemptReason.STUDENT:
                    return TinExemptReasonType.STUDENT;
                case TinExemptReason.HOME_MAKER:
                    return TinExemptReasonType.HOME_MAKER;
                case TinExemptReason.HOUSE_WIFE:
                    return TinExemptReasonType.HOUSE_WIFE;
                case TinExemptReason.RETIRED:
                    return TinExemptReasonType.RETIRED;
                case TinExemptReason.DIPLOMAT:
                    return TinExemptReasonType.DIPLOMAT;
                case TinExemptReason.TIN_NOT_ELIGIBLE:
                    return TinExemptReasonType.TIN_NOT_ELIGIBLE;
                case TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON:
                    return TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON;
                case TinExemptReason.FFI_GIIN_APPLICATION_PENDING:
                    return TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING;
                case TinExemptReason.NFFE_GIIN_APPLICATION_PENDING:
                    return TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING;
                case TinExemptReason.FFI_GIIN_NOT_REQUIRED:
                    return TinExemptReasonType.FFI_GIIN_NOT_REQUIRED;
                case TinExemptReason.NFFE_GIIN_NOT_REQUIRED:
                    return TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED;
                case TinExemptReason.NON_PARTICIPATING_FFI:
                    return TinExemptReasonType.NON_PARTICIPATING_FFI;
                case TinExemptReason.FATCA_EXEMPT_NFFE:
                    return TinExemptReasonType.FATCA_EXEMPT_NFFE;
                case TinExemptReason.OTHERS:
                    return TinExemptReasonType.OTHERS;
                default:
                    throw new IllegalArgumentException("Invalid TIN exempt reason: " + value);
            }
        }
    }
}
