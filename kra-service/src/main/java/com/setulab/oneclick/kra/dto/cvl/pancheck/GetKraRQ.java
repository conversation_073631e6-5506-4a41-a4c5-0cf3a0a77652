package com.setulab.oneclick.kra.dto.cvl.pancheck;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetKraRQ {

    @Pattern(regexp = "^(I|A)$", message = "Invalid fetchType. Allowed values are: I, A.")
    @JsonProperty(value = "fetchType")
    private String fetchType = "I";

    @NotBlank
    @Pattern(regexp = "^[A-Z]{3}[ABCFGHLPT][A-Z][0-9]{4}[A-Z]$", message = "Invalid PAN format. Please use XXXXXXXXXX format.")
    @JsonProperty(value = "panNumber")
    private String panNumber;

    @NotBlank
    @Pattern(regexp = "^(CVLKRA|DOTEX|NDML|CAMS|KARVY)$", message = "Invalid kraCode. Allowed values are: CVLKRA, DOTEX, NDML, CAMS, KARVY.")
    @JsonProperty(value = "kraCode")
    private String kraCode;

    @NotBlank
    @Pattern(regexp = "^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[0-2])\\/\\d{4}$", message = "Invalid date format. Please use dd/MM/yyyy format.")
    @JsonProperty(value = "dob")
    private String dob;
}
