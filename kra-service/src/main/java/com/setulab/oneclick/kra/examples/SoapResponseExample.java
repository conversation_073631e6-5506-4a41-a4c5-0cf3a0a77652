package com.setulab.oneclick.kra.examples;

import com.setulab.oneclick.kra.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
public class SoapResponseExample {

    public static void main(String[] args) {
        // Your exact SOAP response
        String soapResponse = "<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\"><s:Body xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\"><GetPasswordResponse xmlns=\"https://pancheck.www.kracvl.com\"><GetPasswordResult><APP_RES_ROOT xmlns=\"\"><APP_GET_PASS>RpQQWxjCc0cNqpZSQQYlRQ!3d!3d</APP_GET_PASS></APP_RES_ROOT></GetPasswordResult></GetPasswordResponse></s:Body></s:Envelope>";

        log.info("=== SOAP Response Analysis ===");
        log.info("Original XML: {}", soapResponse);

        // Extract APP_GET_PASS using recursive search
        Optional<String> appGetPass = XmlUtils.findFirstTagRecursively(soapResponse, "APP_GET_PASS");
        if (appGetPass.isPresent()) {
            log.info("✅ Found APP_GET_PASS: {}", appGetPass.get());
        } else {
            log.warn("❌ APP_GET_PASS not found");
        }

        // Try different tag names to show the flexibility
        log.info("\n=== Testing Different Tag Names ===");

        // Test with exact tag name
        Optional<String> exactTag = XmlUtils.findFirstTagRecursively(soapResponse, "APP_GET_PASS");
        log.info("Exact tag 'APP_GET_PASS': {}", exactTag.orElse("Not found"));

        // Test with case-insensitive search
        Optional<String> caseInsensitive = XmlUtils.findFirstTagRecursivelyIgnoreCase(soapResponse, "app_get_pass");
        log.info("Case-insensitive 'app_get_pass': {}", caseInsensitive.orElse("Not found"));

        // Test with different tags in the response
        Optional<String> bodyTag = XmlUtils.findFirstTagRecursively(soapResponse, "Body");
        log.info("Body tag: {}", bodyTag.isPresent() ? "Found" : "Not found");

        Optional<String> envelopeTag = XmlUtils.findFirstTagRecursively(soapResponse, "Envelope");
        log.info("Envelope tag: {}", envelopeTag.isPresent() ? "Found" : "Not found");

        Optional<String> getPasswordResponse = XmlUtils.findFirstTagRecursively(soapResponse, "GetPasswordResponse");
        log.info("GetPasswordResponse tag: {}", getPasswordResponse.isPresent() ? "Found" : "Not found");

        Optional<String> appResRoot = XmlUtils.findFirstTagRecursively(soapResponse, "APP_RES_ROOT");
        log.info("APP_RES_ROOT tag: {}", appResRoot.isPresent() ? "Found" : "Not found");

        // Test finding all occurrences of a tag (in case there are multiple)
        log.info("\n=== Finding All Occurrences ===");
        List<String> allBodyTags = XmlUtils.findAllTagsRecursively(soapResponse, "Body");
        log.info("All Body tags found: {}", allBodyTags.size());

        List<String> allAppGetPassTags = XmlUtils.findAllTagsRecursively(soapResponse, "APP_GET_PASS");
        log.info("All APP_GET_PASS tags found: {}", allAppGetPassTags.size());
        if (!allAppGetPassTags.isEmpty()) {
            log.info("Values: {}", allAppGetPassTags);
        }

        // Compare with traditional methods
        log.info("\n=== Comparison with Traditional Methods ===");

        // Traditional method (might not work due to namespaces)
        Optional<String> traditional = XmlUtils.extractTagValue(soapResponse, "APP_GET_PASS");
        log.info("Traditional extractTagValue: {}", traditional.orElse("Not found"));

        // Namespace-aware method
        Optional<String> namespaceAware = XmlUtils.extractTagValueWithNamespace(soapResponse, "", "APP_GET_PASS");
        log.info("Namespace-aware extractTagValueWithNamespace: {}", namespaceAware.orElse("Not found"));

        // Recursive method (should work regardless of structure)
        Optional<String> recursive = XmlUtils.findFirstTagRecursively(soapResponse, "APP_GET_PASS");
        log.info("Recursive findFirstTagRecursively: {}", recursive.orElse("Not found"));

        log.info("\n=== Conclusion ===");
        log.info(
                "The recursive method is most reliable for complex SOAP responses with namespaces and nested structures.");
    }
}