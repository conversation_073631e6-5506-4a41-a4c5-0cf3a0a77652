package com.setulab.oneclick.kra.examples;

import com.setulab.oneclick.kra.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

@Slf4j
public class XmlExtractionExample {

    public static void main(String[] args) {
        // Example 1: Simple XML
        String simpleXml = """
                <response>
                    <status>success</status>
                    <token>abc123</token>
                    <message>Operation completed</message>
                </response>
                """;

        log.info("=== Simple XML Extraction ===");
        Optional<String> status = XmlUtils.extractTagValue(simpleXml, "status");
        log.info("Status: {}", status.orElse("Not found"));

        Optional<String> token = XmlUtils.extractTagValue(simpleXml, "token");
        log.info("Token: {}", token.orElse("Not found"));

        // Example 2: SOAP Response
        String soapResponse = """
                <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Body>
                        <GetPasswordResponse xmlns="http://example.com/">
                            <GetPasswordResult>
                                <token>xyz789</token>
                                <expires>2024-12-31</expires>
                            </GetPasswordResult>
                        </GetPasswordResponse>
                    </soap:Body>
                </soap:Envelope>
                """;

        log.info("=== SOAP Response Extraction ===");
        Optional<String> soapToken = XmlUtils.extractTagValue(soapResponse, "token");
        log.info("SOAP Token: {}", soapToken.orElse("Not found"));

        Optional<String> expires = XmlUtils.extractTagValue(soapResponse, "expires");
        log.info("Expires: {}", expires.orElse("Not found"));

        // Extract with namespace
        Optional<String> soapBody = XmlUtils.extractTagValueWithNamespace(
                soapResponse,
                "http://schemas.xmlsoap.org/soap/envelope/",
                "Body");
        log.info("SOAP Body found: {}", soapBody.isPresent());

        // Example 3: Multiple tags
        String multipleTagsXml = """
                <users>
                    <user>
                        <name>John</name>
                        <age>30</age>
                    </user>
                    <user>
                        <name>Jane</name>
                        <age>25</age>
                    </user>
                </users>
                """;

        log.info("=== Multiple Tags Extraction ===");
        List<String> names = XmlUtils.extractTagValues(multipleTagsXml, "name");
        log.info("Names: {}", names);

        List<String> ages = XmlUtils.extractTagValues(multipleTagsXml, "age");
        log.info("Ages: {}", ages);

        // Example 4: Nested tags
        String nestedXml = """
                <company>
                    <department>
                        <name>IT</name>
                        <employees>
                            <employee>
                                <id>001</id>
                                <name>Alice</name>
                            </employee>
                            <employee>
                                <id>002</id>
                                <name>Bob</name>
                            </employee>
                        </employees>
                    </department>
                </company>
                """;

        log.info("=== Nested Tags Extraction ===");
        Optional<String> deptName = XmlUtils.extractNestedTagValue(nestedXml, "department", "name");
        log.info("Department name: {}", deptName.orElse("Not found"));

        List<String> employeeNames = XmlUtils.extractTagValues(nestedXml, "name");
        log.info("All names: {}", employeeNames);

        // Example 5: XML with attributes
        String xmlWithAttributes = """
                <product id="123" category="electronics">
                    <name>Laptop</name>
                    <price currency="USD">999.99</price>
                </product>
                """;

        log.info("=== XML with Attributes Extraction ===");
        Optional<String> productName = XmlUtils.extractTagValue(xmlWithAttributes, "name");
        log.info("Product name: {}", productName.orElse("Not found"));

        Optional<String> productId = XmlUtils.extractAttributeValue(xmlWithAttributes, "product", "id");
        log.info("Product ID: {}", productId.orElse("Not found"));

        Optional<String> currency = XmlUtils.extractAttributeValue(xmlWithAttributes, "price", "currency");
        log.info("Currency: {}", currency.orElse("Not found"));

        // Example 6: Simple regex extraction (for simple cases)
        log.info("=== Simple Regex Extraction ===");
        Optional<String> simpleStatus = XmlUtils.extractTagValueSimple(simpleXml, "status");
        log.info("Status (regex): {}", simpleStatus.orElse("Not found"));
    }
}