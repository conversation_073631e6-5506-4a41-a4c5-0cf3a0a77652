package com.setulab.oneclick.kra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.setulab.oneclick.kra.client.CvlPancheckJsonServiceClientImpl;
import com.setulab.oneclick.kra.dto.cvl.pancheck.GetTokenRS;
import com.setulab.oneclick.kra.enums.RandomNumberGenerationMethod;
import com.setulab.oneclick.kra.utils.RandomNumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CvlPancheckJsonService {

    private final CvlPancheckJsonServiceClientImpl cvlPancheckJsonServiceClientImpl;

    public GetTokenRS getToken(String passKey) {
        log.info("Initiating GetToken for passKey: {}", passKey);
        if (passKey == null || passKey.isEmpty()) {
            passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
            log.info("PassKey is not provided! Generated new passKey: {}", passKey);
        }

        //Call Json Client
        JsonNode jsonResponse = cvlPancheckJsonServiceClientImpl.GetToken(passKey);
        log.info("Json Response Token: {}", jsonResponse);
        String token = "";
        GetTokenRS getTokenRS = new GetTokenRS();
        getTokenRS.setToken(token);


        return getTokenRS;
    }

    public JsonNode getPanStatus(JsonNode jsonValue) {
        return null;
    }

    public JsonNode solicitPANDetailsFetchALLKRA(JsonNode jsonValue) {
        return null;
    }

    public JsonNode insertUpdateKYCRecord(JsonNode jsonValue) {
        return null;
    }
}
