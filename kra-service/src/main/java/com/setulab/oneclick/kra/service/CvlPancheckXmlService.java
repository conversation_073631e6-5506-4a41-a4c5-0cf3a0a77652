package com.setulab.oneclick.kra.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.setulab.oneclick.kra.client.CvlPancheckXmlServiceClientImpl;
import com.setulab.oneclick.kra.dto.cvl.pancheck.GetKraRQ;
import com.setulab.oneclick.kra.dto.cvl.pancheck.GetPanStatusRQ;
import com.setulab.oneclick.kra.enums.RandomNumberGenerationMethod;
import com.setulab.oneclick.kra.utils.RandomNumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CvlPancheckXmlService {

    private final CvlPancheckXmlServiceClientImpl cvlPancheckXmlServiceClientImpl;

    public JsonNode getToken(String passKey) {
        try {
            log.info("Initiating GetToken for passKey: {}", passKey);
            if (passKey == null || passKey.isEmpty()) {
                passKey = RandomNumberUtils.generateRequestId(RandomNumberGenerationMethod.UUID);
                log.info("PassKey is not provided! Generated new passKey: {}", passKey);
            }

            // Call Xml Client
            JsonNode token = cvlPancheckXmlServiceClientImpl.GetToken(passKey);
            log.info("Token Response: {}", token);
            return token;
        } catch (Exception e) {
            log.error("Error fetching Token: {}", e.getMessage());
            return null;
        }
    }

    public JsonNode getPanStatus(GetPanStatusRQ getPanStatusRQ) {
        try {
            String panNumber = getPanStatusRQ.getPanNumber();
            log.info("Initiating GetPanStatus Xml for panNumber: {}", panNumber);

            // Call Xml Client
            JsonNode panStatus = cvlPancheckXmlServiceClientImpl.GetPanStatus(panNumber);
            log.info("Pan Status Response: {}", panStatus);

            return panStatus;
        } catch (Exception e) {
            log.error("Error fetching Pan Status: {}", e.getMessage());
            return null;
        }
    }

    public JsonNode solicitPANDetailsFetchALLKRA(GetKraRQ getKraRQ) {
        try {
            String panNumber = getKraRQ.getPanNumber();
            String kraCode = getKraRQ.getKraCode();
            String dob = getKraRQ.getDob();
            String fetchType = getKraRQ.getFetchType();

            //Call Xml Client
            JsonNode kraData = cvlPancheckXmlServiceClientImpl.SolicitPANDetailsFetchALLKRA(fetchType, kraCode, panNumber, dob);
            log.info("KRA Data Response: {}", kraData);

            return kraData;
        } catch (Exception e) {
            log.error("Error fetching KRA Data: {}", e.getMessage());
            return null;
        }
    }

    public JsonNode insertUpdateKYCRecord(JsonNode jsonValue) {
        return null;
    }
}
