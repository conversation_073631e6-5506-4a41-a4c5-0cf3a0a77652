package com.setulab.oneclick.kra.utils;

import com.setulab.oneclick.kra.enums.RandomNumberGenerationMethod;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Random;
import java.util.UUID;

public class RandomNumberUtils {
    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";


    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod) {
        return getRandomId(randomNumberGenerationMethod, 16);
    }

    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod, Integer length) {
        return getRandomId(randomNumberGenerationMethod, length);
    }

    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod, Integer length, String prefix) {
        Integer prefixLength = prefix.length();
        String randomId = getRandomId(randomNumberGenerationMethod, length - prefixLength);
        return prefix + randomId;
    }

    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod, Boolean dateTimePrefix) {

        String dateTimePrefixString = getDateTimePrefix();
        return dateTimePrefixString + getRandomId(randomNumberGenerationMethod, 16);
    }

    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod, Integer length, Boolean dateTimePrefix) {

        String dateTimePrefixString = getDateTimePrefix();
        Integer prefixLength = dateTimePrefixString.length();
        String randomId = getRandomId(randomNumberGenerationMethod, length - prefixLength);
        return dateTimePrefixString + randomId;
    }


    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod, Integer length, String prefix, Boolean dateTimePrefix) {
        String dateTimePrefixString = getDateTimePrefix();
        Integer prefixLength = dateTimePrefixString.length() + prefix.length();
        String randomId = getRandomId(randomNumberGenerationMethod, length - prefixLength);
        return prefix + dateTimePrefixString + randomId;
    }

    public static String generateRequestId(RandomNumberGenerationMethod randomNumberGenerationMethod, String prefix, Boolean dateTimePrefix) {
        String dateTimePrefixString = getDateTimePrefix();
        String randomId = getRandomId(randomNumberGenerationMethod, 16);
        return prefix + "::" + dateTimePrefixString + "::" + randomId;
    }


    private static String getDateTimePrefix() {
        return ZonedDateTime.now(ZoneId.of("Asia/Kolkata")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
//        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
//        return String.valueOf(Instant.now().getEpochSecond());
    }


    private static String getRandomId(RandomNumberGenerationMethod randomNumberGenerationMethod, Integer length) {
        return switch (randomNumberGenerationMethod) {
            case UUID -> generateUUID(length);
            case RANDOM -> generateRandom(length);
            case BASE64 -> generateBase64(length);
            case SHA256 -> generateSHA256(length);
            default -> generateUUID(length);
        };
    }


    public static String generateUUID(Integer length) {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replace("-", "").substring(0, length);
    }

    public static String generateRandom(Integer length) {
        StringBuilder requestId = new StringBuilder();
        long nanoTime = System.nanoTime();
        requestId.append(Long.toHexString(nanoTime).toUpperCase());
        Random random = new Random();

        while (requestId.length() < length) {
            int index = random.nextInt("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".length());
            requestId.append("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".charAt(index));
        }

        return requestId.toString();
    }

    public static String generateBase64(Integer length) {
        int byteLength = (int) Math.ceil((double) (length * 6) / 8.0D);
        byte[] randomBytes = new byte[byteLength];
        (new SecureRandom()).nextBytes(randomBytes);
        String base64String = Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
        return base64String.substring(0, Math.min(length, base64String.length()));
    }

    public static String generateSHA256(Integer length) {
        try {
            String input = UUID.randomUUID().toString();
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            byte[] var5 = hash;
            int var6 = hash.length;

            for (int var7 = 0; var7 < var6; ++var7) {
                byte b = var5[var7];
                String hex = Integer.toHexString(255 & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }

                hexString.append(hex);
            }

            return hexString.substring(0, length).toUpperCase();
        } catch (Exception var10) {
            System.out.println(var10.getMessage());
            return null;
        }
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        System.out.println(generateRequestId(RandomNumberGenerationMethod.UUID));
        System.out.println(generateRequestId(RandomNumberGenerationMethod.UUID, "ckyc-verify", true));
    }

}
