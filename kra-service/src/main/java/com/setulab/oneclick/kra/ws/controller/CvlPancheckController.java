package com.setulab.oneclick.kra.ws.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.setulab.oneclick.kra.dto.cvl.pancheck.GetPanStatusRQ;
import com.setulab.oneclick.kra.dto.cvl.pancheck.GetTokenRS;
import com.setulab.oneclick.kra.service.CvlPancheckJsonService;
import com.setulab.oneclick.kra.service.CvlPancheckXmlService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/api/cvl/pancheck")
@RequiredArgsConstructor
public class CvlPancheckController {

    private final CvlPancheckXmlService cvlPancheckXmlService;
    private final CvlPancheckJsonService cvlPancheckJsonService;

    @GetMapping(value = {"", "/"})
    public String root() {
        return "KraService";
    }

    @GetMapping(value = "/health")
    public String health() {
        return "OK";
    }

    @GetMapping(value = "/xml/get-token")
    public ResponseEntity<JsonNode> getToken(@RequestParam(value = "passKey", required = false) String passKey) {
        return ResponseEntity.ok(cvlPancheckXmlService.getToken(passKey));
    }

    @PostMapping(value = "/xml/get-pan-status")
    public ResponseEntity<JsonNode> getPanStatus(@Valid @RequestBody GetPanStatusRQ getPanStatusRQ) {
        return ResponseEntity.ok(cvlPancheckXmlService.getPanStatus(getPanStatusRQ));
    }

    @GetMapping(value = "/xml/solicit-pan-details-fetch-all-kra")
    public ResponseEntity<JsonNode> solicitPANDetailsFetchALLKRA() {
        return ResponseEntity.ok(cvlPancheckXmlService.solicitPANDetailsFetchALLKRA(null));
    }

    @PostMapping(value = "/xml/insert-update-kyc-record")
    public ResponseEntity<JsonNode> insertUpdateKYCRecord() {
        return ResponseEntity.ok(cvlPancheckXmlService.insertUpdateKYCRecord(null));
    }

    @GetMapping(value = "/json/get-token")
    public ResponseEntity<GetTokenRS> jsonGetToken(@RequestParam(value = "passKey", required = false) String passKey) {
        return ResponseEntity.ok(cvlPancheckJsonService.getToken(passKey));
    }

    @GetMapping(value = "/json/get-pan-status")
    public ResponseEntity<JsonNode> jsonGetPanStatus() {
        return ResponseEntity.ok(cvlPancheckJsonService.getPanStatus(null));
    }

    @GetMapping(value = "/json/solicit-pan-details-fetch-all-kra")
    public ResponseEntity<JsonNode> jsonSolicitPANDetailsFetchALLKRA() {
        return ResponseEntity.ok(cvlPancheckJsonService.solicitPANDetailsFetchALLKRA(null));
    }

    @PostMapping(value = "/json/insert-update-kyc-record")
    public ResponseEntity<JsonNode> jsonInsertUpdateKYCRecord() {
        return ResponseEntity.ok(cvlPancheckJsonService.insertUpdateKYCRecord(null));
    }
}
