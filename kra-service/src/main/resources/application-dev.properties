spring.application.name=kra-service
server.port=8083
server.shutdown=graceful

######## Catalog Service Configuration  #########
catalog.page-size=10

######## Actuator Configuration  #########
management.info.git.mode=full
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
management.tracing.enabled=false
management.tracing.sampling.probability=1.0

######## Swagger Configuration  #########
swagger.api-gateway-url=http://localhost:8080/kra

######## KRA Configuration  #########
kra.json.base-url=https://api.kra.gov.in/json
kra.xml.base-url=https://api.kra.gov.in/xml
kra.json.password=your-kra-json-password
kra.xml.password=your-kra-xml-password

######## CVL Pancheck XML Configuration  #########
kra.cvl.pancheck.xml.base-url=https://pancheck.www.kracvl.com
kra.cvl.pancheck.xml.username=NEWEKYC
kra.cvl.pancheck.xml.password=acml@1234
kra.cvl.pancheck.xml.poscode=1100043000
kra.cvl.pancheck.xml.rtacode=1100043000

######## CVL Pancheck JSON Configuration  #########
kra.cvl.pancheck.json.user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
kra.cvl.pancheck.json.base-url=https://api.cvl.com/json
kra.cvl.pancheck.json.apikey=your-json-api-key
kra.cvl.pancheck.json.aeskey=your-json-aes-key
kra.cvl.pancheck.json.username=your-json-username
kra.cvl.pancheck.json.password=your-json-password
kra.cvl.pancheck.json.poscode=JSON001
kra.cvl.pancheck.json.rtacode=JSONRTA001
kra.cvl.pancheck.json.token-valid-time=3600