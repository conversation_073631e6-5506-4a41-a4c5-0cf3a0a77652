package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for CvlKraPancheckAdditionalPepStatus
 */
class AdditionalPepStatusTest {

    @Test
    void testPepStatusConversions() {
        // Test asCode
        assertEquals(AdditionalPepStatus.NOT_APPLICABLE_CODE, AdditionalPepStatus.PepStatus.NOT_APPLICABLE.asCode());
        assertEquals(AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE, AdditionalPepStatus.PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON.asCode());
        assertEquals(AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON_CODE, AdditionalPepStatus.PepStatus.POLITICALLY_EXPOSED_PERSON.asCode());

        // Test asDescription
        assertEquals("Not Applicable", AdditionalPepStatus.PepStatus.NOT_APPLICABLE.asDescription());
        assertEquals("Related To Politically Exposed Person", AdditionalPepStatus.PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON.asDescription());
        assertEquals("Politically Exposed Person", AdditionalPepStatus.PepStatus.POLITICALLY_EXPOSED_PERSON.asDescription());

        // Test fromCode
        assertEquals(Optional.of(AdditionalPepStatus.PepStatus.NOT_APPLICABLE), AdditionalPepStatus.PepStatus.fromCode(AdditionalPepStatus.NOT_APPLICABLE_CODE));
        assertEquals(Optional.of(AdditionalPepStatus.PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON), AdditionalPepStatus.PepStatus.fromCode(AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE));
        assertEquals(Optional.of(AdditionalPepStatus.PepStatus.POLITICALLY_EXPOSED_PERSON), AdditionalPepStatus.PepStatus.fromCode(AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON_CODE));
        assertEquals(Optional.empty(), AdditionalPepStatus.PepStatus.fromCode("INVALID"));
        assertEquals(Optional.empty(), AdditionalPepStatus.PepStatus.fromCode(null));

        // Test fromDescription
        assertEquals(AdditionalPepStatus.PepStatus.NOT_APPLICABLE, AdditionalPepStatus.PepStatus.fromDescription(AdditionalPepStatus.NOT_APPLICABLE_DESC));
        assertEquals(AdditionalPepStatus.PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON, AdditionalPepStatus.PepStatus.fromDescription(AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON_DESC));
        assertEquals(AdditionalPepStatus.PepStatus.POLITICALLY_EXPOSED_PERSON, AdditionalPepStatus.PepStatus.fromDescription(AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON_DESC));

        // Test fromDescription with invalid input
        assertThrows(IllegalArgumentException.class, () -> AdditionalPepStatus.PepStatus.fromDescription("INVALID"));
        assertThrows(IllegalArgumentException.class, () -> AdditionalPepStatus.PepStatus.fromDescription(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("NA", AdditionalPepStatus.NOT_APPLICABLE_CODE);
        assertEquals("NOT_APPLICABLE", AdditionalPepStatus.NOT_APPLICABLE);
        assertEquals("PEP", AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON_CODE);
        assertEquals("POLITICALLY_EXPOSED_PERSON", AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON);
        assertEquals("RPEP", AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE);
        assertEquals("RELATED_TO_POLITICALLY_EXPOSED_PERSON", AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON);
    }

    @Test
    void testAdditionalPepStatusCodes() {
        // Test that the lists contain the expected values
        assertTrue(AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES.contains(AdditionalPepStatus.NOT_APPLICABLE_CODE));
        assertTrue(AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES.contains(AdditionalPepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON_CODE));
        assertTrue(AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES.contains(AdditionalPepStatus.POLITICALLY_EXPOSED_PERSON_CODE));
        assertEquals(3, AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES.size());

        assertTrue(AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES_AS_ENUM.contains(AdditionalPepStatus.PepStatus.NOT_APPLICABLE));
        assertTrue(AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES_AS_ENUM.contains(AdditionalPepStatus.PepStatus.RELATED_TO_POLITICALLY_EXPOSED_PERSON));
        assertTrue(AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES_AS_ENUM.contains(AdditionalPepStatus.PepStatus.POLITICALLY_EXPOSED_PERSON));
        assertEquals(3, AdditionalPepStatus.ADDITIONAL_PEP_STATUS_CODES_AS_ENUM.size());
    }
}