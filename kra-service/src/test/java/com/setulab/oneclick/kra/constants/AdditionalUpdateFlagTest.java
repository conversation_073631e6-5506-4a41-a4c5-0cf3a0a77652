package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for AdditionalUpdateFlag
 */
class AdditionalUpdateFlagTest {

    @Test
    void testAdditionalUpdateFlagConversions() {
        // Test asCode
        assertEquals(AdditionalUpdateFlag.NEW_CODE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.NEW.asCode());
        assertEquals(AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS_CODE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS.asCode());
        assertEquals(AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS_CODE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS.asCode());
        assertEquals(AdditionalUpdateFlag.DELETE_CODE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.DELETE.asCode());
        assertEquals(AdditionalUpdateFlag.OLD_KYC_RECORD_CODE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.OLD_KYC_RECORD.asCode());
        assertEquals(AdditionalUpdateFlag.INTEROP_MODIFICATION_CODE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.INTEROP_MODIFICATION.asCode());

        // Test asDescription
        assertEquals(AdditionalUpdateFlag.NEW_DESC, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.NEW.asDescription());
        assertEquals(AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS_DESC, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS.asDescription());
        assertEquals(AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS_DESC, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS.asDescription());
        assertEquals(AdditionalUpdateFlag.DELETE_DESC, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.DELETE.asDescription());
        assertEquals(AdditionalUpdateFlag.OLD_KYC_RECORD_DESC, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.OLD_KYC_RECORD.asDescription());
        assertEquals(AdditionalUpdateFlag.INTEROP_MODIFICATION_DESC, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.INTEROP_MODIFICATION.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(AdditionalUpdateFlag.AdditionalUpdateFlagType.NEW),
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(AdditionalUpdateFlag.NEW_CODE));
        assertEquals(
                Optional.of(AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS),
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS_CODE));
        assertEquals(
                Optional.of(AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS),
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS_CODE));
        assertEquals(
                Optional.of(AdditionalUpdateFlag.AdditionalUpdateFlagType.DELETE),
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(AdditionalUpdateFlag.DELETE_CODE));
        assertEquals(
                Optional.of(AdditionalUpdateFlag.AdditionalUpdateFlagType.OLD_KYC_RECORD),
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(AdditionalUpdateFlag.OLD_KYC_RECORD_CODE));
        assertEquals(
                Optional.of(AdditionalUpdateFlag.AdditionalUpdateFlagType.INTEROP_MODIFICATION),
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(AdditionalUpdateFlag.INTEROP_MODIFICATION_CODE));
        assertEquals(Optional.empty(), 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromCode(null));

        // Test fromString
        assertEquals(AdditionalUpdateFlag.AdditionalUpdateFlagType.NEW, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(AdditionalUpdateFlag.NEW));
        assertEquals(AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS));
        assertEquals(AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS));
        assertEquals(AdditionalUpdateFlag.AdditionalUpdateFlagType.DELETE, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(AdditionalUpdateFlag.DELETE));
        assertEquals(AdditionalUpdateFlag.AdditionalUpdateFlagType.OLD_KYC_RECORD, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(AdditionalUpdateFlag.OLD_KYC_RECORD));
        assertEquals(AdditionalUpdateFlag.AdditionalUpdateFlagType.INTEROP_MODIFICATION, 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(AdditionalUpdateFlag.INTEROP_MODIFICATION));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> AdditionalUpdateFlag.AdditionalUpdateFlagType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("NEW", AdditionalUpdateFlag.NEW);
        assertEquals("01", AdditionalUpdateFlag.NEW_CODE);
        assertEquals("New", AdditionalUpdateFlag.NEW_DESC);
        assertEquals("MODIFY_WITH_DOCUMENTS", AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS);
        assertEquals("02", AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS_CODE);
        assertEquals("Modify with documents", AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS_DESC);
        assertEquals("MODIFY_WITHOUT_DOCUMENTS", AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS);
        assertEquals("03", AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS_CODE);
        assertEquals("Modify without documents", AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS_DESC);
        assertEquals("DELETE", AdditionalUpdateFlag.DELETE);
        assertEquals("04", AdditionalUpdateFlag.DELETE_CODE);
        assertEquals("Delete", AdditionalUpdateFlag.DELETE_DESC);
        assertEquals("OLD_KYC_RECORD", AdditionalUpdateFlag.OLD_KYC_RECORD);
        assertEquals("07", AdditionalUpdateFlag.OLD_KYC_RECORD_CODE);
        assertEquals("Old KYC Record", AdditionalUpdateFlag.OLD_KYC_RECORD_DESC);
        assertEquals("INTEROP_MODIFICATION", AdditionalUpdateFlag.INTEROP_MODIFICATION);
        assertEquals("99", AdditionalUpdateFlag.INTEROP_MODIFICATION_CODE);
        assertEquals("Interop Modification", AdditionalUpdateFlag.INTEROP_MODIFICATION_DESC);
    }

    @Test
    void testAdditionalUpdateFlagCodes() {
        // Test that the lists contain the expected values
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.contains(AdditionalUpdateFlag.NEW_CODE));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.contains(AdditionalUpdateFlag.MODIFY_WITH_DOCUMENTS_CODE));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.contains(AdditionalUpdateFlag.MODIFY_WITHOUT_DOCUMENTS_CODE));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.contains(AdditionalUpdateFlag.DELETE_CODE));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.contains(AdditionalUpdateFlag.OLD_KYC_RECORD_CODE));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.contains(AdditionalUpdateFlag.INTEROP_MODIFICATION_CODE));
        assertEquals(6, AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES.size());

        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.contains(
                AdditionalUpdateFlag.AdditionalUpdateFlagType.NEW));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.contains(
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.contains(
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.contains(
                AdditionalUpdateFlag.AdditionalUpdateFlagType.DELETE));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.contains(
                AdditionalUpdateFlag.AdditionalUpdateFlagType.OLD_KYC_RECORD));
        assertTrue(AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.contains(
                AdditionalUpdateFlag.AdditionalUpdateFlagType.INTEROP_MODIFICATION));
        assertEquals(6, AdditionalUpdateFlag.ADDITIONAL_UPDATE_FLAG_CODES_AS_ENUM.size());
    }

    @Test
    void testSpecificUpdateFlags() {
        // Test specific update flag types
        assertEquals("New", 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.NEW.asDescription());
        assertEquals("Modify with documents", 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITH_DOCUMENTS.asDescription());
        assertEquals("Modify without documents", 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.MODIFY_WITHOUT_DOCUMENTS.asDescription());
        assertEquals("Delete", 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.DELETE.asDescription());
        assertEquals("Old KYC Record", 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.OLD_KYC_RECORD.asDescription());
        assertEquals("Interop Modification", 
                AdditionalUpdateFlag.AdditionalUpdateFlagType.INTEROP_MODIFICATION.asDescription());
    }
}
