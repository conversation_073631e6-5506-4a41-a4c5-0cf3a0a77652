package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for AddressProof
 */
class AddressProofTest {

    @Test
    void testAddressProofConversions() {
        // Test asCode
        assertEquals(AddressProof.PASSPORT_CODE, 
                AddressProof.AddressProofType.PASSPORT.asCode());
        assertEquals(AddressProof.DRIVING_LICENSE_CODE, 
                AddressProof.AddressProofType.DRIVING_LICENSE.asCode());
        assertEquals(AddressProof.BANK_PASSBOOK_CODE, 
                AddressProof.AddressProofType.BANK_PASSBOOK.asCode());
        assertEquals(AddressProof.AADHAAR_CODE, 
                AddressProof.AddressProofType.AADHAAR.asCode());
        assertEquals(AddressProof.OTHER_CODE, 
                AddressProof.AddressProofType.OTHER.asCode());

        // Test asDescription
        assertEquals("Passport", 
                AddressProof.AddressProofType.PASSPORT.asDescription());
        assertEquals("Driving License", 
                AddressProof.AddressProofType.DRIVING_LICENSE.asDescription());
        assertEquals("Latest Bank Passbook", 
                AddressProof.AddressProofType.BANK_PASSBOOK.asDescription());
        assertEquals("AADHAAR", 
                AddressProof.AddressProofType.AADHAAR.asDescription());
        assertEquals("ANY OTHER PROOF OF ADDRESS", 
                AddressProof.AddressProofType.OTHER.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(AddressProof.AddressProofType.PASSPORT),
                AddressProof.AddressProofType.fromCode(AddressProof.PASSPORT_CODE));
        assertEquals(
                Optional.of(AddressProof.AddressProofType.DRIVING_LICENSE),
                AddressProof.AddressProofType.fromCode(AddressProof.DRIVING_LICENSE_CODE));
        assertEquals(
                Optional.of(AddressProof.AddressProofType.BANK_PASSBOOK),
                AddressProof.AddressProofType.fromCode(AddressProof.BANK_PASSBOOK_CODE));
        assertEquals(
                Optional.of(AddressProof.AddressProofType.AADHAAR),
                AddressProof.AddressProofType.fromCode(AddressProof.AADHAAR_CODE));
        assertEquals(
                Optional.of(AddressProof.AddressProofType.OTHER),
                AddressProof.AddressProofType.fromCode(AddressProof.OTHER_CODE));
        assertEquals(Optional.empty(), 
                AddressProof.AddressProofType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                AddressProof.AddressProofType.fromCode(null));

        // Test fromString
        assertEquals(AddressProof.AddressProofType.PASSPORT, 
                AddressProof.AddressProofType.fromString(AddressProof.PASSPORT));
        assertEquals(AddressProof.AddressProofType.DRIVING_LICENSE, 
                AddressProof.AddressProofType.fromString(AddressProof.DRIVING_LICENSE));
        assertEquals(AddressProof.AddressProofType.BANK_PASSBOOK, 
                AddressProof.AddressProofType.fromString(AddressProof.BANK_PASSBOOK));
        assertEquals(AddressProof.AddressProofType.AADHAAR, 
                AddressProof.AddressProofType.fromString(AddressProof.AADHAAR));
        assertEquals(AddressProof.AddressProofType.OTHER, 
                AddressProof.AddressProofType.fromString(AddressProof.OTHER));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> AddressProof.AddressProofType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> AddressProof.AddressProofType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that key constants are correctly defined
        assertEquals("PASSPORT", AddressProof.PASSPORT);
        assertEquals("01", AddressProof.PASSPORT_CODE);
        assertEquals("DRIVING_LICENSE", AddressProof.DRIVING_LICENSE);
        assertEquals("02", AddressProof.DRIVING_LICENSE_CODE);
        assertEquals("BANK_PASSBOOK", AddressProof.BANK_PASSBOOK);
        assertEquals("03", AddressProof.BANK_PASSBOOK_CODE);
        assertEquals("AADHAAR", AddressProof.AADHAAR);
        assertEquals("31", AddressProof.AADHAAR_CODE);
        assertEquals("OTHER", AddressProof.OTHER);
        assertEquals("32", AddressProof.OTHER_CODE);
    }

    @Test
    void testAddressProofCodes() {
        // Test that the lists contain the expected values
        assertTrue(AddressProof.ADDRESS_PROOF_CODES.contains(AddressProof.PASSPORT_CODE));
        assertTrue(AddressProof.ADDRESS_PROOF_CODES.contains(AddressProof.DRIVING_LICENSE_CODE));
        assertTrue(AddressProof.ADDRESS_PROOF_CODES.contains(AddressProof.BANK_PASSBOOK_CODE));
        assertTrue(AddressProof.ADDRESS_PROOF_CODES.contains(AddressProof.AADHAAR_CODE));
        assertTrue(AddressProof.ADDRESS_PROOF_CODES.contains(AddressProof.OTHER_CODE));
        assertEquals(31, AddressProof.ADDRESS_PROOF_CODES.size());
    }

    @Test
    void testSpecificAddressProofTypes() {
        // Test some specific important address proof types
        assertEquals(AddressProof.VOTER_ID_CARD_CODE, 
                AddressProof.AddressProofType.VOTER_ID_CARD.asCode());
        assertEquals(AddressProof.RATION_CARD_CODE, 
                AddressProof.AddressProofType.RATION_CARD.asCode());
        assertEquals(AddressProof.ELECTRICITY_BILL_CODE, 
                AddressProof.AddressProofType.ELECTRICITY_BILL.asCode());
        assertEquals(AddressProof.NAREGA_JOB_CARD_CODE, 
                AddressProof.AddressProofType.NAREGA_JOB_CARD.asCode());

        // Test descriptions
        assertEquals("Voter Identity Card", 
                AddressProof.AddressProofType.VOTER_ID_CARD.asDescription());
        assertEquals("Ration Card", 
                AddressProof.AddressProofType.RATION_CARD.asDescription());
        assertEquals("Latest Electricity Bill", 
                AddressProof.AddressProofType.ELECTRICITY_BILL.asDescription());
        assertEquals("NREGA JOB CARD", 
                AddressProof.AddressProofType.NAREGA_JOB_CARD.asDescription());
    }

    @Test
    void testGovernmentIdCards() {
        // Test government-issued ID cards
        assertEquals(AddressProof.CENTRAL_STATE_GOVERNMENT_ID_CARD_CODE, 
                AddressProof.AddressProofType.CENTRAL_STATE_GOVERNMENT_ID_CARD.asCode());
        assertEquals(AddressProof.STATUTORY_REGULATORY_AUTHORITIES_ID_CARD_CODE, 
                AddressProof.AddressProofType.STATUTORY_REGULATORY_AUTHORITIES_ID_CARD.asCode());
        assertEquals(AddressProof.PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD_CODE, 
                AddressProof.AddressProofType.PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD.asCode());

        // Test their descriptions
        assertEquals("ID Card with address issued by Central / State Government", 
                AddressProof.AddressProofType.CENTRAL_STATE_GOVERNMENT_ID_CARD.asDescription());
        assertEquals("ID Card with address issued by Statutory / Regulatory Authorities", 
                AddressProof.AddressProofType.STATUTORY_REGULATORY_AUTHORITIES_ID_CARD.asDescription());
        assertEquals("ID Card with address issued by Public Sector Undertakings", 
                AddressProof.AddressProofType.PUBLIC_SECTOR_UNDERTAKINGS_ID_CARD.asDescription());
    }
}
