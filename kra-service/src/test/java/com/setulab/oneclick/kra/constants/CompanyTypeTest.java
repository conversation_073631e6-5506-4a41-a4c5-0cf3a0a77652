package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for CompanyType
 */
class CompanyTypeTest {

    @Test
    void testCompanyTypeConversions() {
        // Test asCode for key company types
        assertEquals(CompanyType.PRIVATE_LTD_COMPANY_CODE, 
                CompanyType.CompanyTypeType.PRIVATE_LTD_COMPANY.asCode());
        assertEquals(CompanyType.PUBLIC_LTD_COMPANY_CODE, 
                CompanyType.CompanyTypeType.PUBLIC_LTD_COMPANY.asCode());
        assertEquals(CompanyType.BODY_CORPORATE_CODE, 
                CompanyType.CompanyTypeType.BODY_CORPORATE.asCode());
        assertEquals(CompanyType.PARTNERSHIP_CODE, 
                CompanyType.CompanyTypeType.PARTNERSHIP.asCode());
        assertEquals(CompanyType.HUF_CODE, 
                CompanyType.CompanyTypeType.HUF.asCode());
        assertEquals(CompanyType.BANK_CODE, 
                CompanyType.CompanyTypeType.BANK.asCode());
        assertEquals(CompanyType.LLP_CODE, 
                CompanyType.CompanyTypeType.LLP.asCode());
        assertEquals(CompanyType.OTHERS_CODE, 
                CompanyType.CompanyTypeType.OTHERS.asCode());

        // Test asDescription
        assertEquals(CompanyType.PRIVATE_LTD_COMPANY_DESC, 
                CompanyType.CompanyTypeType.PRIVATE_LTD_COMPANY.asDescription());
        assertEquals(CompanyType.PUBLIC_LTD_COMPANY_DESC, 
                CompanyType.CompanyTypeType.PUBLIC_LTD_COMPANY.asDescription());
        assertEquals(CompanyType.BODY_CORPORATE_DESC, 
                CompanyType.CompanyTypeType.BODY_CORPORATE.asDescription());
        assertEquals(CompanyType.PARTNERSHIP_DESC, 
                CompanyType.CompanyTypeType.PARTNERSHIP.asDescription());
        assertEquals(CompanyType.HUF_DESC, 
                CompanyType.CompanyTypeType.HUF.asDescription());
        assertEquals(CompanyType.BANK_DESC, 
                CompanyType.CompanyTypeType.BANK.asDescription());
        assertEquals(CompanyType.LLP_DESC, 
                CompanyType.CompanyTypeType.LLP.asDescription());
        assertEquals(CompanyType.OTHERS_DESC, 
                CompanyType.CompanyTypeType.OTHERS.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.PRIVATE_LTD_COMPANY),
                CompanyType.CompanyTypeType.fromCode(CompanyType.PRIVATE_LTD_COMPANY_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.PUBLIC_LTD_COMPANY),
                CompanyType.CompanyTypeType.fromCode(CompanyType.PUBLIC_LTD_COMPANY_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.BODY_CORPORATE),
                CompanyType.CompanyTypeType.fromCode(CompanyType.BODY_CORPORATE_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.PARTNERSHIP),
                CompanyType.CompanyTypeType.fromCode(CompanyType.PARTNERSHIP_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.HUF),
                CompanyType.CompanyTypeType.fromCode(CompanyType.HUF_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.BANK),
                CompanyType.CompanyTypeType.fromCode(CompanyType.BANK_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.LLP),
                CompanyType.CompanyTypeType.fromCode(CompanyType.LLP_CODE));
        assertEquals(
                Optional.of(CompanyType.CompanyTypeType.OTHERS),
                CompanyType.CompanyTypeType.fromCode(CompanyType.OTHERS_CODE));
        assertEquals(Optional.empty(), 
                CompanyType.CompanyTypeType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                CompanyType.CompanyTypeType.fromCode(null));

        // Test fromString
        assertEquals(CompanyType.CompanyTypeType.PRIVATE_LTD_COMPANY, 
                CompanyType.CompanyTypeType.fromString(CompanyType.PRIVATE_LTD_COMPANY));
        assertEquals(CompanyType.CompanyTypeType.PUBLIC_LTD_COMPANY, 
                CompanyType.CompanyTypeType.fromString(CompanyType.PUBLIC_LTD_COMPANY));
        assertEquals(CompanyType.CompanyTypeType.BODY_CORPORATE, 
                CompanyType.CompanyTypeType.fromString(CompanyType.BODY_CORPORATE));
        assertEquals(CompanyType.CompanyTypeType.PARTNERSHIP, 
                CompanyType.CompanyTypeType.fromString(CompanyType.PARTNERSHIP));
        assertEquals(CompanyType.CompanyTypeType.HUF, 
                CompanyType.CompanyTypeType.fromString(CompanyType.HUF));
        assertEquals(CompanyType.CompanyTypeType.BANK, 
                CompanyType.CompanyTypeType.fromString(CompanyType.BANK));
        assertEquals(CompanyType.CompanyTypeType.LLP, 
                CompanyType.CompanyTypeType.fromString(CompanyType.LLP));
        assertEquals(CompanyType.CompanyTypeType.OTHERS, 
                CompanyType.CompanyTypeType.fromString(CompanyType.OTHERS));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> CompanyType.CompanyTypeType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> CompanyType.CompanyTypeType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that key constants are correctly defined
        assertEquals("PRIVATE_LTD_COMPANY", CompanyType.PRIVATE_LTD_COMPANY);
        assertEquals("01", CompanyType.PRIVATE_LTD_COMPANY_CODE);
        assertEquals("Private Limited Company", CompanyType.PRIVATE_LTD_COMPANY_DESC);
        assertEquals("PUBLIC_LTD_COMPANY", CompanyType.PUBLIC_LTD_COMPANY);
        assertEquals("02", CompanyType.PUBLIC_LTD_COMPANY_CODE);
        assertEquals("Public Limited Company", CompanyType.PUBLIC_LTD_COMPANY_DESC);
        assertEquals("BODY_CORPORATE", CompanyType.BODY_CORPORATE);
        assertEquals("03", CompanyType.BODY_CORPORATE_CODE);
        assertEquals("Body Corporate", CompanyType.BODY_CORPORATE_DESC);
        assertEquals("PARTNERSHIP", CompanyType.PARTNERSHIP);
        assertEquals("04", CompanyType.PARTNERSHIP_CODE);
        assertEquals("Partnership", CompanyType.PARTNERSHIP_DESC);
        assertEquals("HUF", CompanyType.HUF);
        assertEquals("08", CompanyType.HUF_CODE);
        assertEquals("HUF", CompanyType.HUF_DESC);
        assertEquals("BANK", CompanyType.BANK);
        assertEquals("10", CompanyType.BANK_CODE);
        assertEquals("Bank", CompanyType.BANK_DESC);
        assertEquals("LLP", CompanyType.LLP);
        assertEquals("16", CompanyType.LLP_CODE);
        assertEquals("LLP", CompanyType.LLP_DESC);
        assertEquals("OTHERS", CompanyType.OTHERS);
        assertEquals("99", CompanyType.OTHERS_CODE);
        assertEquals("Others", CompanyType.OTHERS_DESC);
    }

    @Test
    void testCompanyTypeCodes() {
        // Test that the lists contain the expected values
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.PRIVATE_LTD_COMPANY_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.PUBLIC_LTD_COMPANY_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.BODY_CORPORATE_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.PARTNERSHIP_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.HUF_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.BANK_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.LLP_CODE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES.contains(CompanyType.OTHERS_CODE));
        assertEquals(21, CompanyType.COMPANY_TYPE_CODES.size());

        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.PRIVATE_LTD_COMPANY));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.PUBLIC_LTD_COMPANY));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.BODY_CORPORATE));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.PARTNERSHIP));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.HUF));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.BANK));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.LLP));
        assertTrue(CompanyType.COMPANY_TYPE_CODES_AS_ENUM.contains(
                CompanyType.CompanyTypeType.OTHERS));
        assertEquals(21, CompanyType.COMPANY_TYPE_CODES_AS_ENUM.size());
    }

    @Test
    void testFinancialInstitutionTypes() {
        // Test financial institution specific types
        assertEquals("FI", CompanyType.CompanyTypeType.FI.asDescription());
        assertEquals("FII", CompanyType.CompanyTypeType.FII.asDescription());
        assertEquals("QFI", CompanyType.CompanyTypeType.QFI.asDescription());
        assertEquals("Bank", CompanyType.CompanyTypeType.BANK.asDescription());
        
        assertEquals(CompanyType.FI_CODE, CompanyType.CompanyTypeType.FI.asCode());
        assertEquals(CompanyType.FII_CODE, CompanyType.CompanyTypeType.FII.asCode());
        assertEquals(CompanyType.QFI_CODE, CompanyType.CompanyTypeType.QFI.asCode());
        assertEquals(CompanyType.BANK_CODE, CompanyType.CompanyTypeType.BANK.asCode());
    }

    @Test
    void testGovernmentAndNGOTypes() {
        // Test government and NGO types
        assertEquals("Government Body", 
                CompanyType.CompanyTypeType.GOVERNMENT_BODY.asDescription());
        assertEquals("Non Government Organisation", 
                CompanyType.CompanyTypeType.NON_GOVERNMENT_ORGANISATION.asDescription());
        assertEquals("Defense Establishment", 
                CompanyType.CompanyTypeType.DEFENSE_ESTABLISHMENT.asDescription());
        assertEquals("Trust Charities NGOs", 
                CompanyType.CompanyTypeType.TRUST_CHARITIES_NGOS.asDescription());
        assertEquals("Society", 
                CompanyType.CompanyTypeType.SOCIETY.asDescription());
    }

    @Test
    void testForeignInvestorCategories() {
        // Test foreign investor categories
        assertEquals("Eligible Foreign Investor Category I", 
                CompanyType.CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I.asDescription());
        assertEquals("Eligible Foreign Investor Category II", 
                CompanyType.CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II.asDescription());
        assertEquals("Eligible Foreign Investor Category III", 
                CompanyType.CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asDescription());
        
        assertEquals(CompanyType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I_CODE, 
                CompanyType.CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_I.asCode());
        assertEquals(CompanyType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II_CODE, 
                CompanyType.CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_II.asCode());
        assertEquals(CompanyType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE, 
                CompanyType.CompanyTypeType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asCode());
    }
}
