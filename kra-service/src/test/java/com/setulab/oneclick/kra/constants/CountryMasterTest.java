package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for CountryMaster
 */
class CountryMasterTest {

    @Test
    void testCountryMasterConversions() {
        // Test asCode
        assertEquals(CountryMaster.INDIA_CODE, 
                CountryMaster.CountryMasterType.INDIA.asCode());
        assertEquals(CountryMaster.ALBANIA_CODE, 
                CountryMaster.CountryMasterType.ALBANIA.asCode());

        // Test asDescription
        assertEquals(CountryMaster.INDIA_DESC, 
                CountryMaster.CountryMasterType.INDIA.asDescription());
        assertEquals(CountryMaster.ALBANIA_DESC, 
                CountryMaster.CountryMasterType.ALBANIA.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(CountryMaster.CountryMasterType.INDIA),
                CountryMaster.CountryMasterType.fromCode(CountryMaster.INDIA_CODE));
        assertEquals(
                Optional.of(CountryMaster.CountryMasterType.ALBANIA),
                CountryMaster.CountryMasterType.fromCode(CountryMaster.ALBANIA_CODE));
        assertEquals(Optional.empty(), 
                CountryMaster.CountryMasterType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                CountryMaster.CountryMasterType.fromCode(null));

        // Test fromString
        assertEquals(CountryMaster.CountryMasterType.INDIA, 
                CountryMaster.CountryMasterType.fromString(CountryMaster.INDIA));
        assertEquals(CountryMaster.CountryMasterType.ALBANIA, 
                CountryMaster.CountryMasterType.fromString(CountryMaster.ALBANIA));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> CountryMaster.CountryMasterType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> CountryMaster.CountryMasterType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("INDIA", CountryMaster.INDIA);
        assertEquals("101", CountryMaster.INDIA_CODE);
        assertEquals("India", CountryMaster.INDIA_DESC);
        assertEquals("ALBANIA", CountryMaster.ALBANIA);
        assertEquals("003", CountryMaster.ALBANIA_CODE);
        assertEquals("Albania", CountryMaster.ALBANIA_DESC);
    }

    @Test
    void testCountryCodes() {
        // Test that the lists contain the expected values
        assertTrue(CountryMaster.COUNTRY_CODES.contains(CountryMaster.INDIA_CODE));
        assertTrue(CountryMaster.COUNTRY_CODES.contains(CountryMaster.ALBANIA_CODE));
        assertEquals(2, CountryMaster.COUNTRY_CODES.size());

        assertTrue(CountryMaster.COUNTRY_CODES_AS_ENUM.contains(
                CountryMaster.CountryMasterType.INDIA));
        assertTrue(CountryMaster.COUNTRY_CODES_AS_ENUM.contains(
                CountryMaster.CountryMasterType.ALBANIA));
        assertEquals(2, CountryMaster.COUNTRY_CODES_AS_ENUM.size());
    }

    @Test
    void testSpecificCountries() {
        // Test specific countries
        assertEquals("India", 
                CountryMaster.CountryMasterType.INDIA.asDescription());
        assertEquals("Albania", 
                CountryMaster.CountryMasterType.ALBANIA.asDescription());
        
        assertEquals("101", CountryMaster.CountryMasterType.INDIA.asCode());
        assertEquals("003", CountryMaster.CountryMasterType.ALBANIA.asCode());
    }
}
