package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for DistrictMaster
 */
class DistrictMasterTest {

    @Test
    void testDistrictMasterConversions() {
        // Test asCode
        assertEquals(DistrictMaster.DISTRICT_1, 
                DistrictMaster.DistrictMasterType.DISTRICT_1.asCode());
        assertEquals(DistrictMaster.DISTRICT_2, 
                DistrictMaster.DistrictMasterType.DISTRICT_2.asCode());
        assertEquals(DistrictMaster.DISTRICT_3, 
                DistrictMaster.DistrictMasterType.DISTRICT_3.asCode());
        assertEquals(DistrictMaster.DISTRICT_4, 
                DistrictMaster.DistrictMasterType.DISTRICT_4.asCode());
        assertEquals(DistrictMaster.DISTRICT_5, 
                DistrictMaster.DistrictMasterType.DISTRICT_5.asCode());

        // Test asDescription
        assertEquals(DistrictMaster.DISTRICT_1_DESC, 
                DistrictMaster.DistrictMasterType.DISTRICT_1.asDescription());
        assertEquals(DistrictMaster.DISTRICT_2_DESC, 
                DistrictMaster.DistrictMasterType.DISTRICT_2.asDescription());
        assertEquals(DistrictMaster.DISTRICT_3_DESC, 
                DistrictMaster.DistrictMasterType.DISTRICT_3.asDescription());
        assertEquals(DistrictMaster.DISTRICT_4_DESC, 
                DistrictMaster.DistrictMasterType.DISTRICT_4.asDescription());
        assertEquals(DistrictMaster.DISTRICT_5_DESC, 
                DistrictMaster.DistrictMasterType.DISTRICT_5.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(DistrictMaster.DistrictMasterType.DISTRICT_1),
                DistrictMaster.DistrictMasterType.fromCode(DistrictMaster.DISTRICT_1));
        assertEquals(
                Optional.of(DistrictMaster.DistrictMasterType.DISTRICT_2),
                DistrictMaster.DistrictMasterType.fromCode(DistrictMaster.DISTRICT_2));
        assertEquals(
                Optional.of(DistrictMaster.DistrictMasterType.DISTRICT_3),
                DistrictMaster.DistrictMasterType.fromCode(DistrictMaster.DISTRICT_3));
        assertEquals(
                Optional.of(DistrictMaster.DistrictMasterType.DISTRICT_4),
                DistrictMaster.DistrictMasterType.fromCode(DistrictMaster.DISTRICT_4));
        assertEquals(
                Optional.of(DistrictMaster.DistrictMasterType.DISTRICT_5),
                DistrictMaster.DistrictMasterType.fromCode(DistrictMaster.DISTRICT_5));
        assertEquals(Optional.empty(), 
                DistrictMaster.DistrictMasterType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                DistrictMaster.DistrictMasterType.fromCode(null));

        // Test fromString
        assertEquals(DistrictMaster.DistrictMasterType.DISTRICT_1, 
                DistrictMaster.DistrictMasterType.fromString(DistrictMaster.DISTRICT_1));
        assertEquals(DistrictMaster.DistrictMasterType.DISTRICT_2, 
                DistrictMaster.DistrictMasterType.fromString(DistrictMaster.DISTRICT_2));
        assertEquals(DistrictMaster.DistrictMasterType.DISTRICT_3, 
                DistrictMaster.DistrictMasterType.fromString(DistrictMaster.DISTRICT_3));
        assertEquals(DistrictMaster.DistrictMasterType.DISTRICT_4, 
                DistrictMaster.DistrictMasterType.fromString(DistrictMaster.DISTRICT_4));
        assertEquals(DistrictMaster.DistrictMasterType.DISTRICT_5, 
                DistrictMaster.DistrictMasterType.fromString(DistrictMaster.DISTRICT_5));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> DistrictMaster.DistrictMasterType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> DistrictMaster.DistrictMasterType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("1", DistrictMaster.DISTRICT_1);
        assertEquals("KRA", DistrictMaster.DISTRICT_1_DESC);
        assertEquals("2", DistrictMaster.DISTRICT_2);
        assertEquals("KRA", DistrictMaster.DISTRICT_2_DESC);
        assertEquals("3", DistrictMaster.DISTRICT_3);
        assertEquals("KRA", DistrictMaster.DISTRICT_3_DESC);
        assertEquals("4", DistrictMaster.DISTRICT_4);
        assertEquals("KRA", DistrictMaster.DISTRICT_4_DESC);
        assertEquals("5", DistrictMaster.DISTRICT_5);
        assertEquals("KRA", DistrictMaster.DISTRICT_5_DESC);
    }

    @Test
    void testDistrictCodes() {
        // Test that the lists contain the expected values
        assertTrue(DistrictMaster.DISTRICT_CODES.contains(DistrictMaster.DISTRICT_1));
        assertTrue(DistrictMaster.DISTRICT_CODES.contains(DistrictMaster.DISTRICT_2));
        assertTrue(DistrictMaster.DISTRICT_CODES.contains(DistrictMaster.DISTRICT_3));
        assertTrue(DistrictMaster.DISTRICT_CODES.contains(DistrictMaster.DISTRICT_4));
        assertTrue(DistrictMaster.DISTRICT_CODES.contains(DistrictMaster.DISTRICT_5));
        assertEquals(5, DistrictMaster.DISTRICT_CODES.size());

        assertTrue(DistrictMaster.DISTRICT_CODES_AS_ENUM.contains(
                DistrictMaster.DistrictMasterType.DISTRICT_1));
        assertTrue(DistrictMaster.DISTRICT_CODES_AS_ENUM.contains(
                DistrictMaster.DistrictMasterType.DISTRICT_2));
        assertTrue(DistrictMaster.DISTRICT_CODES_AS_ENUM.contains(
                DistrictMaster.DistrictMasterType.DISTRICT_3));
        assertTrue(DistrictMaster.DISTRICT_CODES_AS_ENUM.contains(
                DistrictMaster.DistrictMasterType.DISTRICT_4));
        assertTrue(DistrictMaster.DISTRICT_CODES_AS_ENUM.contains(
                DistrictMaster.DistrictMasterType.DISTRICT_5));
        assertEquals(5, DistrictMaster.DISTRICT_CODES_AS_ENUM.size());
    }

    @Test
    void testAllDistrictsHaveSameDescription() {
        // Test that all districts have the same description "KRA"
        assertEquals("KRA", DistrictMaster.DistrictMasterType.DISTRICT_1.asDescription());
        assertEquals("KRA", DistrictMaster.DistrictMasterType.DISTRICT_2.asDescription());
        assertEquals("KRA", DistrictMaster.DistrictMasterType.DISTRICT_3.asDescription());
        assertEquals("KRA", DistrictMaster.DistrictMasterType.DISTRICT_4.asDescription());
        assertEquals("KRA", DistrictMaster.DistrictMasterType.DISTRICT_5.asDescription());
    }
}
