package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for ExemptionCategory
 */
class ExemptionCategoryTest {

    @Test
    void testExemptionCategoryConversions() {
        // Test asCode
        assertEquals(ExemptionCategory.SIKKIM_RESIDENT_CODE, 
                ExemptionCategory.ExemptionCategoryType.SIKKIM_RESIDENT.asCode());
        assertEquals(ExemptionCategory.STATE_GOVT_CODE, 
                ExemptionCategory.ExemptionCategoryType.STATE_GOVT.asCode());
        assertEquals(ExemptionCategory.CENTRAL_GOVT_CODE, 
                ExemptionCategory.ExemptionCategoryType.CENTRAL_GOVT.asCode());
        assertEquals(ExemptionCategory.OTHER_DOCUMENTS_CODE, 
                ExemptionCategory.ExemptionCategoryType.OTHER_DOCUMENTS.asCode());

        // Test asDescription
        assertEquals(ExemptionCategory.SIKKIM_RESIDENT_DESC, 
                ExemptionCategory.ExemptionCategoryType.SIKKIM_RESIDENT.asDescription());
        assertEquals(ExemptionCategory.STATE_GOVT_DESC, 
                ExemptionCategory.ExemptionCategoryType.STATE_GOVT.asDescription());
        assertEquals(ExemptionCategory.CENTRAL_GOVT_DESC, 
                ExemptionCategory.ExemptionCategoryType.CENTRAL_GOVT.asDescription());
        assertEquals(ExemptionCategory.OTHER_DOCUMENTS_DESC, 
                ExemptionCategory.ExemptionCategoryType.OTHER_DOCUMENTS.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(ExemptionCategory.ExemptionCategoryType.SIKKIM_RESIDENT),
                ExemptionCategory.ExemptionCategoryType.fromCode(ExemptionCategory.SIKKIM_RESIDENT_CODE));
        assertEquals(
                Optional.of(ExemptionCategory.ExemptionCategoryType.STATE_GOVT),
                ExemptionCategory.ExemptionCategoryType.fromCode(ExemptionCategory.STATE_GOVT_CODE));
        assertEquals(
                Optional.of(ExemptionCategory.ExemptionCategoryType.CENTRAL_GOVT),
                ExemptionCategory.ExemptionCategoryType.fromCode(ExemptionCategory.CENTRAL_GOVT_CODE));
        assertEquals(
                Optional.of(ExemptionCategory.ExemptionCategoryType.OTHER_DOCUMENTS),
                ExemptionCategory.ExemptionCategoryType.fromCode(ExemptionCategory.OTHER_DOCUMENTS_CODE));
        assertEquals(Optional.empty(), 
                ExemptionCategory.ExemptionCategoryType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                ExemptionCategory.ExemptionCategoryType.fromCode(null));

        // Test fromString
        assertEquals(ExemptionCategory.ExemptionCategoryType.SIKKIM_RESIDENT, 
                ExemptionCategory.ExemptionCategoryType.fromString(ExemptionCategory.SIKKIM_RESIDENT));
        assertEquals(ExemptionCategory.ExemptionCategoryType.STATE_GOVT, 
                ExemptionCategory.ExemptionCategoryType.fromString(ExemptionCategory.STATE_GOVT));
        assertEquals(ExemptionCategory.ExemptionCategoryType.CENTRAL_GOVT, 
                ExemptionCategory.ExemptionCategoryType.fromString(ExemptionCategory.CENTRAL_GOVT));
        assertEquals(ExemptionCategory.ExemptionCategoryType.OTHER_DOCUMENTS, 
                ExemptionCategory.ExemptionCategoryType.fromString(ExemptionCategory.OTHER_DOCUMENTS));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> ExemptionCategory.ExemptionCategoryType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> ExemptionCategory.ExemptionCategoryType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("SIKKIM_RESIDENT", ExemptionCategory.SIKKIM_RESIDENT);
        assertEquals("01", ExemptionCategory.SIKKIM_RESIDENT_CODE);
        assertEquals("Sikkim Resident", ExemptionCategory.SIKKIM_RESIDENT_DESC);
        assertEquals("STATE_GOVT", ExemptionCategory.STATE_GOVT);
        assertEquals("02", ExemptionCategory.STATE_GOVT_CODE);
        assertEquals("State Government", ExemptionCategory.STATE_GOVT_DESC);
        assertEquals("CENTRAL_GOVT", ExemptionCategory.CENTRAL_GOVT);
        assertEquals("03", ExemptionCategory.CENTRAL_GOVT_CODE);
        assertEquals("Central Government", ExemptionCategory.CENTRAL_GOVT_DESC);
        assertEquals("OTHER_DOCUMENTS", ExemptionCategory.OTHER_DOCUMENTS);
        assertEquals("11", ExemptionCategory.OTHER_DOCUMENTS_CODE);
        assertEquals("Other Documents", ExemptionCategory.OTHER_DOCUMENTS_DESC);
    }

    @Test
    void testExemptionCategoryCodes() {
        // Test that the lists contain the expected values
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.SIKKIM_RESIDENT_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.STATE_GOVT_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.CENTRAL_GOVT_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.COURT_APPOINTED_OFFICIALS_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.UN_ENTITY_MULTILATERAL_AGENCY_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.OFFICIAL_LIQUIDATOR_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.COURT_RECEIVER_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.SIP_OF_MUTUAL_FUNDS_UPTO_50K_CODE));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES.contains(ExemptionCategory.OTHER_DOCUMENTS_CODE));
        assertEquals(9, ExemptionCategory.EXEMPTION_CATEGORY_CODES.size());

        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES_AS_ENUM.contains(
                ExemptionCategory.ExemptionCategoryType.SIKKIM_RESIDENT));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES_AS_ENUM.contains(
                ExemptionCategory.ExemptionCategoryType.STATE_GOVT));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES_AS_ENUM.contains(
                ExemptionCategory.ExemptionCategoryType.CENTRAL_GOVT));
        assertTrue(ExemptionCategory.EXEMPTION_CATEGORY_CODES_AS_ENUM.contains(
                ExemptionCategory.ExemptionCategoryType.OTHER_DOCUMENTS));
        assertEquals(9, ExemptionCategory.EXEMPTION_CATEGORY_CODES_AS_ENUM.size());
    }
}
