package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for ExemptionIdProof
 */
class ExemptionIdProofTest {

    @Test
    void testExemptionIdProofConversions() {
        // Test asCode for key ID proof types
        assertEquals(ExemptionIdProof.PAN_CODE, 
                ExemptionIdProof.ExemptionIdProofType.PAN.asCode());
        assertEquals(ExemptionIdProof.UID_NO_CODE, 
                ExemptionIdProof.ExemptionIdProofType.UID_NO.asCode());
        assertEquals(ExemptionIdProof.PASSPORT_CODE, 
                ExemptionIdProof.ExemptionIdProofType.PASSPORT.asCode());
        assertEquals(ExemptionIdProof.DRIVING_LICENSE_CODE, 
                ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE.asCode());
        assertEquals(ExemptionIdProof.VOTER_IDENTITY_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD.asCode());
        assertEquals(ExemptionIdProof.OTHER_ID_PROOF_CODE, 
                ExemptionIdProof.ExemptionIdProofType.OTHER_ID_PROOF.asCode());

        // Test asDescription
        assertEquals(ExemptionIdProof.PAN_DESC, 
                ExemptionIdProof.ExemptionIdProofType.PAN.asDescription());
        assertEquals(ExemptionIdProof.UID_NO_DESC, 
                ExemptionIdProof.ExemptionIdProofType.UID_NO.asDescription());
        assertEquals(ExemptionIdProof.PASSPORT_DESC, 
                ExemptionIdProof.ExemptionIdProofType.PASSPORT.asDescription());
        assertEquals(ExemptionIdProof.DRIVING_LICENSE_DESC, 
                ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE.asDescription());
        assertEquals(ExemptionIdProof.VOTER_IDENTITY_CARD_DESC, 
                ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD.asDescription());
        assertEquals(ExemptionIdProof.OTHER_ID_PROOF_DESC, 
                ExemptionIdProof.ExemptionIdProofType.OTHER_ID_PROOF.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(ExemptionIdProof.ExemptionIdProofType.PAN),
                ExemptionIdProof.ExemptionIdProofType.fromCode(ExemptionIdProof.PAN_CODE));
        assertEquals(
                Optional.of(ExemptionIdProof.ExemptionIdProofType.UID_NO),
                ExemptionIdProof.ExemptionIdProofType.fromCode(ExemptionIdProof.UID_NO_CODE));
        assertEquals(
                Optional.of(ExemptionIdProof.ExemptionIdProofType.PASSPORT),
                ExemptionIdProof.ExemptionIdProofType.fromCode(ExemptionIdProof.PASSPORT_CODE));
        assertEquals(
                Optional.of(ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE),
                ExemptionIdProof.ExemptionIdProofType.fromCode(ExemptionIdProof.DRIVING_LICENSE_CODE));
        assertEquals(
                Optional.of(ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD),
                ExemptionIdProof.ExemptionIdProofType.fromCode(ExemptionIdProof.VOTER_IDENTITY_CARD_CODE));
        assertEquals(
                Optional.of(ExemptionIdProof.ExemptionIdProofType.OTHER_ID_PROOF),
                ExemptionIdProof.ExemptionIdProofType.fromCode(ExemptionIdProof.OTHER_ID_PROOF_CODE));
        assertEquals(Optional.empty(), 
                ExemptionIdProof.ExemptionIdProofType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                ExemptionIdProof.ExemptionIdProofType.fromCode(null));

        // Test fromString
        assertEquals(ExemptionIdProof.ExemptionIdProofType.PAN, 
                ExemptionIdProof.ExemptionIdProofType.fromString(ExemptionIdProof.PAN));
        assertEquals(ExemptionIdProof.ExemptionIdProofType.UID_NO, 
                ExemptionIdProof.ExemptionIdProofType.fromString(ExemptionIdProof.UID_NO));
        assertEquals(ExemptionIdProof.ExemptionIdProofType.PASSPORT, 
                ExemptionIdProof.ExemptionIdProofType.fromString(ExemptionIdProof.PASSPORT));
        assertEquals(ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE, 
                ExemptionIdProof.ExemptionIdProofType.fromString(ExemptionIdProof.DRIVING_LICENSE));
        assertEquals(ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD, 
                ExemptionIdProof.ExemptionIdProofType.fromString(ExemptionIdProof.VOTER_IDENTITY_CARD));
        assertEquals(ExemptionIdProof.ExemptionIdProofType.OTHER_ID_PROOF, 
                ExemptionIdProof.ExemptionIdProofType.fromString(ExemptionIdProof.OTHER_ID_PROOF));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> ExemptionIdProof.ExemptionIdProofType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> ExemptionIdProof.ExemptionIdProofType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that key constants are correctly defined
        assertEquals("PAN", ExemptionIdProof.PAN);
        assertEquals("01", ExemptionIdProof.PAN_CODE);
        assertEquals("PAN", ExemptionIdProof.PAN_DESC);
        assertEquals("UID_NO", ExemptionIdProof.UID_NO);
        assertEquals("02", ExemptionIdProof.UID_NO_CODE);
        assertEquals("UID Number", ExemptionIdProof.UID_NO_DESC);
        assertEquals("PASSPORT", ExemptionIdProof.PASSPORT);
        assertEquals("03", ExemptionIdProof.PASSPORT_CODE);
        assertEquals("Passport", ExemptionIdProof.PASSPORT_DESC);
        assertEquals("DRIVING_LICENSE", ExemptionIdProof.DRIVING_LICENSE);
        assertEquals("04", ExemptionIdProof.DRIVING_LICENSE_CODE);
        assertEquals("Driving License", ExemptionIdProof.DRIVING_LICENSE_DESC);
        assertEquals("VOTER_IDENTITY_CARD", ExemptionIdProof.VOTER_IDENTITY_CARD);
        assertEquals("05", ExemptionIdProof.VOTER_IDENTITY_CARD_CODE);
        assertEquals("Voter Identity Card", ExemptionIdProof.VOTER_IDENTITY_CARD_DESC);
        assertEquals("OTHER_ID_PROOF", ExemptionIdProof.OTHER_ID_PROOF);
        assertEquals("16", ExemptionIdProof.OTHER_ID_PROOF_CODE);
        assertEquals("Other ID Proof", ExemptionIdProof.OTHER_ID_PROOF_DESC);
    }

    @Test
    void testExemptionIdProofCodes() {
        // Test that the lists contain the expected values
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.contains(ExemptionIdProof.PAN_CODE));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.contains(ExemptionIdProof.UID_NO_CODE));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.contains(ExemptionIdProof.PASSPORT_CODE));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.contains(ExemptionIdProof.DRIVING_LICENSE_CODE));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.contains(ExemptionIdProof.VOTER_IDENTITY_CARD_CODE));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.contains(ExemptionIdProof.OTHER_ID_PROOF_CODE));
        assertEquals(14, ExemptionIdProof.EXEMPTION_ID_PROOF_CODES.size());

        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.contains(
                ExemptionIdProof.ExemptionIdProofType.PAN));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.contains(
                ExemptionIdProof.ExemptionIdProofType.UID_NO));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.contains(
                ExemptionIdProof.ExemptionIdProofType.PASSPORT));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.contains(
                ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.contains(
                ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD));
        assertTrue(ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.contains(
                ExemptionIdProof.ExemptionIdProofType.OTHER_ID_PROOF));
        assertEquals(14, ExemptionIdProof.EXEMPTION_ID_PROOF_CODES_AS_ENUM.size());
    }

    @Test
    void testGovernmentIssuedIdCards() {
        // Test government-issued ID cards
        assertEquals("ID Card with Applicant Photo issued by Central/State Government and its Departments", 
                ExemptionIdProof.ExemptionIdProofType.CENTRAL_STATE_GOVT_ID_CARD.asDescription());
        assertEquals("ID Card with Applicant Photo issued by Statutory/Regulatory Authorities", 
                ExemptionIdProof.ExemptionIdProofType.STATUTORY_REGULATORY_ID_CARD.asDescription());
        assertEquals("ID Card with Applicant Photo issued by Public Sector Undertakings", 
                ExemptionIdProof.ExemptionIdProofType.PUBLIC_SECTOR_ID_CARD.asDescription());
        
        assertEquals(ExemptionIdProof.CENTRAL_STATE_GOVT_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.CENTRAL_STATE_GOVT_ID_CARD.asCode());
        assertEquals(ExemptionIdProof.STATUTORY_REGULATORY_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.STATUTORY_REGULATORY_ID_CARD.asCode());
        assertEquals(ExemptionIdProof.PUBLIC_SECTOR_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.PUBLIC_SECTOR_ID_CARD.asCode());
    }

    @Test
    void testFinancialInstitutionIdCards() {
        // Test financial institution ID cards
        assertEquals("ID Card with Applicant Photo issued by Scheduled Commercial Banks", 
                ExemptionIdProof.ExemptionIdProofType.COMMERCIAL_BANK_ID_CARD.asDescription());
        assertEquals("ID Card with Applicant Photo issued by Public Financial Institutions", 
                ExemptionIdProof.ExemptionIdProofType.FINANCIAL_INSTITUTION_ID_CARD.asDescription());
        assertEquals("Credit Card with Photo of Applicant issued by Banks", 
                ExemptionIdProof.ExemptionIdProofType.CREDIT_CARD_WITH_PHOTO.asDescription());
        
        assertEquals(ExemptionIdProof.COMMERCIAL_BANK_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.COMMERCIAL_BANK_ID_CARD.asCode());
        assertEquals(ExemptionIdProof.FINANCIAL_INSTITUTION_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.FINANCIAL_INSTITUTION_ID_CARD.asCode());
        assertEquals(ExemptionIdProof.CREDIT_CARD_WITH_PHOTO_CODE, 
                ExemptionIdProof.ExemptionIdProofType.CREDIT_CARD_WITH_PHOTO.asCode());
    }

    @Test
    void testEducationalAndProfessionalIdCards() {
        // Test educational and professional ID cards
        assertEquals("ID Card with Applicant Photo issued by Colleges affiliated to Universities", 
                ExemptionIdProof.ExemptionIdProofType.COLLEGE_ID_CARD.asDescription());
        assertEquals("ID Card issued by Professional Bodies such as ICAI, ICWAI, ICSI, Bar Council etc. to their members", 
                ExemptionIdProof.ExemptionIdProofType.PROFESSIONAL_BODY_ID_CARD.asDescription());
        
        assertEquals(ExemptionIdProof.COLLEGE_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.COLLEGE_ID_CARD.asCode());
        assertEquals(ExemptionIdProof.PROFESSIONAL_BODY_ID_CARD_CODE, 
                ExemptionIdProof.ExemptionIdProofType.PROFESSIONAL_BODY_ID_CARD.asCode());
    }

    @Test
    void testPrimaryIdProofs() {
        // Test primary ID proofs
        assertEquals("PAN", ExemptionIdProof.ExemptionIdProofType.PAN.asDescription());
        assertEquals("UID Number", ExemptionIdProof.ExemptionIdProofType.UID_NO.asDescription());
        assertEquals("Passport", ExemptionIdProof.ExemptionIdProofType.PASSPORT.asDescription());
        assertEquals("Driving License", ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE.asDescription());
        assertEquals("Voter Identity Card", ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD.asDescription());
        
        assertEquals("01", ExemptionIdProof.ExemptionIdProofType.PAN.asCode());
        assertEquals("02", ExemptionIdProof.ExemptionIdProofType.UID_NO.asCode());
        assertEquals("03", ExemptionIdProof.ExemptionIdProofType.PASSPORT.asCode());
        assertEquals("04", ExemptionIdProof.ExemptionIdProofType.DRIVING_LICENSE.asCode());
        assertEquals("05", ExemptionIdProof.ExemptionIdProofType.VOTER_IDENTITY_CARD.asCode());
    }
}
