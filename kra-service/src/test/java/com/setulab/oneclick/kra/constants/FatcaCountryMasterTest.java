package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for FatcaCountryMaster
 */
class FatcaCountryMasterTest {

    @Test
    void testFatcaCountryMasterConversions() {
        // Test asCode
        assertEquals(FatcaCountryMaster.UNITED_STATES_OF_AMERICA, 
                FatcaCountryMaster.FatcaCountryMasterType.UNITED_STATES_OF_AMERICA.asCode());
        assertEquals(FatcaCountryMaster.UNITED_KINGDOM, 
                FatcaCountryMaster.FatcaCountryMasterType.UNITED_KINGDOM.asCode());
        assertEquals(FatcaCountryMaster.CANADA, 
                FatcaCountryMaster.FatcaCountryMasterType.CANADA.asCode());
        assertEquals(FatcaCountryMaster.AUSTRALIA, 
                FatcaCountryMaster.FatcaCountryMasterType.AUSTRALIA.asCode());
        assertEquals(FatcaCountryMaster.NEW_ZEALAND, 
                FatcaCountryMaster.FatcaCountryMasterType.NEW_ZEALAND.asCode());

        // Test asDescription
        assertEquals(FatcaCountryMaster.UNITED_STATES_OF_AMERICA_DESC, 
                FatcaCountryMaster.FatcaCountryMasterType.UNITED_STATES_OF_AMERICA.asDescription());
        assertEquals(FatcaCountryMaster.UNITED_KINGDOM_DESC, 
                FatcaCountryMaster.FatcaCountryMasterType.UNITED_KINGDOM.asDescription());
        assertEquals(FatcaCountryMaster.CANADA_DESC, 
                FatcaCountryMaster.FatcaCountryMasterType.CANADA.asDescription());
        assertEquals(FatcaCountryMaster.AUSTRALIA_DESC, 
                FatcaCountryMaster.FatcaCountryMasterType.AUSTRALIA.asDescription());
        assertEquals(FatcaCountryMaster.NEW_ZEALAND_DESC, 
                FatcaCountryMaster.FatcaCountryMasterType.NEW_ZEALAND.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(FatcaCountryMaster.FatcaCountryMasterType.UNITED_STATES_OF_AMERICA),
                FatcaCountryMaster.FatcaCountryMasterType.fromCode(FatcaCountryMaster.UNITED_STATES_OF_AMERICA));
        assertEquals(
                Optional.of(FatcaCountryMaster.FatcaCountryMasterType.UNITED_KINGDOM),
                FatcaCountryMaster.FatcaCountryMasterType.fromCode(FatcaCountryMaster.UNITED_KINGDOM));
        assertEquals(
                Optional.of(FatcaCountryMaster.FatcaCountryMasterType.CANADA),
                FatcaCountryMaster.FatcaCountryMasterType.fromCode(FatcaCountryMaster.CANADA));
        assertEquals(
                Optional.of(FatcaCountryMaster.FatcaCountryMasterType.AUSTRALIA),
                FatcaCountryMaster.FatcaCountryMasterType.fromCode(FatcaCountryMaster.AUSTRALIA));
        assertEquals(
                Optional.of(FatcaCountryMaster.FatcaCountryMasterType.NEW_ZEALAND),
                FatcaCountryMaster.FatcaCountryMasterType.fromCode(FatcaCountryMaster.NEW_ZEALAND));
        assertEquals(Optional.empty(), 
                FatcaCountryMaster.FatcaCountryMasterType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                FatcaCountryMaster.FatcaCountryMasterType.fromCode(null));

        // Test fromString
        assertEquals(FatcaCountryMaster.FatcaCountryMasterType.UNITED_STATES_OF_AMERICA, 
                FatcaCountryMaster.FatcaCountryMasterType.fromString(FatcaCountryMaster.UNITED_STATES_OF_AMERICA));
        assertEquals(FatcaCountryMaster.FatcaCountryMasterType.UNITED_KINGDOM, 
                FatcaCountryMaster.FatcaCountryMasterType.fromString(FatcaCountryMaster.UNITED_KINGDOM));
        assertEquals(FatcaCountryMaster.FatcaCountryMasterType.CANADA, 
                FatcaCountryMaster.FatcaCountryMasterType.fromString(FatcaCountryMaster.CANADA));
        assertEquals(FatcaCountryMaster.FatcaCountryMasterType.AUSTRALIA, 
                FatcaCountryMaster.FatcaCountryMasterType.fromString(FatcaCountryMaster.AUSTRALIA));
        assertEquals(FatcaCountryMaster.FatcaCountryMasterType.NEW_ZEALAND, 
                FatcaCountryMaster.FatcaCountryMasterType.fromString(FatcaCountryMaster.NEW_ZEALAND));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> FatcaCountryMaster.FatcaCountryMasterType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> FatcaCountryMaster.FatcaCountryMasterType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("1", FatcaCountryMaster.UNITED_STATES_OF_AMERICA);
        assertEquals("United States of America", FatcaCountryMaster.UNITED_STATES_OF_AMERICA_DESC);
        assertEquals("2", FatcaCountryMaster.UNITED_KINGDOM);
        assertEquals("United Kingdom", FatcaCountryMaster.UNITED_KINGDOM_DESC);
        assertEquals("3", FatcaCountryMaster.CANADA);
        assertEquals("Canada", FatcaCountryMaster.CANADA_DESC);
        assertEquals("4", FatcaCountryMaster.AUSTRALIA);
        assertEquals("Australia", FatcaCountryMaster.AUSTRALIA_DESC);
        assertEquals("5", FatcaCountryMaster.NEW_ZEALAND);
        assertEquals("New Zealand", FatcaCountryMaster.NEW_ZEALAND_DESC);
    }

    @Test
    void testFatcaCountryMasterCodes() {
        // Test that the lists contain the expected values
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES.contains(FatcaCountryMaster.UNITED_STATES_OF_AMERICA));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES.contains(FatcaCountryMaster.UNITED_KINGDOM));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES.contains(FatcaCountryMaster.CANADA));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES.contains(FatcaCountryMaster.AUSTRALIA));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES.contains(FatcaCountryMaster.NEW_ZEALAND));
        assertEquals(5, FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES.size());

        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES_AS_ENUM.contains(
                FatcaCountryMaster.FatcaCountryMasterType.UNITED_STATES_OF_AMERICA));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES_AS_ENUM.contains(
                FatcaCountryMaster.FatcaCountryMasterType.UNITED_KINGDOM));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES_AS_ENUM.contains(
                FatcaCountryMaster.FatcaCountryMasterType.CANADA));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES_AS_ENUM.contains(
                FatcaCountryMaster.FatcaCountryMasterType.AUSTRALIA));
        assertTrue(FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES_AS_ENUM.contains(
                FatcaCountryMaster.FatcaCountryMasterType.NEW_ZEALAND));
        assertEquals(5, FatcaCountryMaster.FATCA_COUNTRY_MASTER_CODES_AS_ENUM.size());
    }
}
