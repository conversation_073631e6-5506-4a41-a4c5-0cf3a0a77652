package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for FatcaOtherServices
 */
class FatcaOtherServicesTest {

    @Test
    void testFatcaOtherServicesConversions() {
        // Test asCode
        assertEquals(FatcaOtherServices.CAT1, 
                FatcaOtherServices.FatcaOtherServicesType.CAT1.asCode());
        assertEquals(FatcaOtherServices.CAT2, 
                FatcaOtherServices.FatcaOtherServicesType.CAT2.asCode());
        assertEquals(FatcaOtherServices.CAT3, 
                FatcaOtherServices.FatcaOtherServicesType.CAT3.asCode());
        assertEquals(FatcaOtherServices.CT12, 
                FatcaOtherServices.FatcaOtherServicesType.CT12.asCode());
        assertEquals(FatcaOtherServices.CT23, 
                FatcaOtherServices.FatcaOtherServicesType.CT23.asCode());
        assertEquals(FatcaOtherServices.CT13, 
                FatcaOtherServices.FatcaOtherServicesType.CT13.asCode());
        assertEquals(FatcaOtherServices.CALL, 
                FatcaOtherServices.FatcaOtherServicesType.CALL.asCode());

        // Test asDescription
        assertEquals(FatcaOtherServices.CAT1_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CAT1.asDescription());
        assertEquals(FatcaOtherServices.CAT2_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CAT2.asDescription());
        assertEquals(FatcaOtherServices.CAT3_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CAT3.asDescription());
        assertEquals(FatcaOtherServices.CT12_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CT12.asDescription());
        assertEquals(FatcaOtherServices.CT23_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CT23.asDescription());
        assertEquals(FatcaOtherServices.CT13_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CT13.asDescription());
        assertEquals(FatcaOtherServices.CALL_DESC, 
                FatcaOtherServices.FatcaOtherServicesType.CALL.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CAT1),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CAT1));
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CAT2),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CAT2));
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CAT3),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CAT3));
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CT12),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CT12));
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CT23),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CT23));
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CT13),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CT13));
        assertEquals(
                Optional.of(FatcaOtherServices.FatcaOtherServicesType.CALL),
                FatcaOtherServices.FatcaOtherServicesType.fromCode(FatcaOtherServices.CALL));
        assertEquals(Optional.empty(), 
                FatcaOtherServices.FatcaOtherServicesType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                FatcaOtherServices.FatcaOtherServicesType.fromCode(null));

        // Test fromString
        assertEquals(FatcaOtherServices.FatcaOtherServicesType.CAT1, 
                FatcaOtherServices.FatcaOtherServicesType.fromString(FatcaOtherServices.CAT1));
        assertEquals(FatcaOtherServices.FatcaOtherServicesType.CAT2, 
                FatcaOtherServices.FatcaOtherServicesType.fromString(FatcaOtherServices.CAT2));
        assertEquals(FatcaOtherServices.FatcaOtherServicesType.CAT3, 
                FatcaOtherServices.FatcaOtherServicesType.fromString(FatcaOtherServices.CAT3));
        assertEquals(FatcaOtherServices.FatcaOtherServicesType.CALL, 
                FatcaOtherServices.FatcaOtherServicesType.fromString(FatcaOtherServices.CALL));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> FatcaOtherServices.FatcaOtherServicesType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> FatcaOtherServices.FatcaOtherServicesType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("CAT1", FatcaOtherServices.CAT1);
        assertEquals("Foreign Exchange / Money Changer Services", FatcaOtherServices.CAT1_DESC);
        assertEquals("CAT2", FatcaOtherServices.CAT2);
        assertEquals("Gaming / Gambling / Lottery Services", FatcaOtherServices.CAT2_DESC);
        assertEquals("CAT3", FatcaOtherServices.CAT3);
        assertEquals("Money Laundering / Pawning", FatcaOtherServices.CAT3_DESC);
        assertEquals("CT12", FatcaOtherServices.CT12);
        assertEquals("CT23", FatcaOtherServices.CT23);
        assertEquals("CT13", FatcaOtherServices.CT13);
        assertEquals("CALL", FatcaOtherServices.CALL);
        assertEquals("If All 3", FatcaOtherServices.CALL_DESC);
    }

    @Test
    void testFatcaOtherServicesCodes() {
        // Test that the lists contain the expected values
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CAT1));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CAT2));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CAT3));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CT12));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CT23));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CT13));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.contains(FatcaOtherServices.CALL));
        assertEquals(7, FatcaOtherServices.FATCA_OTHER_SERVICES_CODES.size());

        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES_AS_ENUM.contains(
                FatcaOtherServices.FatcaOtherServicesType.CAT1));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES_AS_ENUM.contains(
                FatcaOtherServices.FatcaOtherServicesType.CAT2));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES_AS_ENUM.contains(
                FatcaOtherServices.FatcaOtherServicesType.CAT3));
        assertTrue(FatcaOtherServices.FATCA_OTHER_SERVICES_CODES_AS_ENUM.contains(
                FatcaOtherServices.FatcaOtherServicesType.CALL));
        assertEquals(7, FatcaOtherServices.FATCA_OTHER_SERVICES_CODES_AS_ENUM.size());
    }

    @Test
    void testCombinationServices() {
        // Test combination services specifically
        assertEquals("Foreign Exchange / Money Changer Services & Gaming / Gambling / Lottery Services", 
                FatcaOtherServices.FatcaOtherServicesType.CT12.asDescription());
        assertEquals("Gaming / Gambling / Lottery Services & Money Laundering / Pawning", 
                FatcaOtherServices.FatcaOtherServicesType.CT23.asDescription());
        assertEquals("Foreign Exchange / Money Changer Services & Money Laundering / Pawning", 
                FatcaOtherServices.FatcaOtherServicesType.CT13.asDescription());
        assertEquals("If All 3", 
                FatcaOtherServices.FatcaOtherServicesType.CALL.asDescription());
    }
}
