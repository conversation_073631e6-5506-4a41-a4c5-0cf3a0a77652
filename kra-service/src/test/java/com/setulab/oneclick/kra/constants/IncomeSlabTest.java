package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for IncomeSlab
 */
class IncomeSlabTest {

    @Test
    void testIncomeSlabConversions() {
        // Test asCode
        assertEquals(IncomeSlab.BELOW_1_LAC_CODE, 
                IncomeSlab.IncomeSlabType.BELOW_1_LAC.asCode());
        assertEquals(IncomeSlab.ONE_TO_FIVE_LAC_CODE, 
                IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC.asCode());
        assertEquals(IncomeSlab.FIVE_TO_TEN_LAC_CODE, 
                IncomeSlab.IncomeSlabType.FIVE_TO_TEN_LAC.asCode());
        assertEquals(IncomeSlab.TEN_TO_TWENTY_FIVE_LAC_CODE, 
                IncomeSlab.IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC.asCode());
        assertEquals(IncomeSlab.ABOVE_TWENTY_FIVE_LAC_CODE, 
                IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC.asCode());
        assertEquals(IncomeSlab.TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE, 
                IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE.asCode());
        assertEquals(IncomeSlab.ABOVE_ONE_CRORE_CODE, 
                IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE.asCode());

        // Test asDescription
        assertEquals(IncomeSlab.BELOW_1_LAC_DESC, 
                IncomeSlab.IncomeSlabType.BELOW_1_LAC.asDescription());
        assertEquals(IncomeSlab.ONE_TO_FIVE_LAC_DESC, 
                IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC.asDescription());
        assertEquals(IncomeSlab.FIVE_TO_TEN_LAC_DESC, 
                IncomeSlab.IncomeSlabType.FIVE_TO_TEN_LAC.asDescription());
        assertEquals(IncomeSlab.TEN_TO_TWENTY_FIVE_LAC_DESC, 
                IncomeSlab.IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC.asDescription());
        assertEquals(IncomeSlab.ABOVE_TWENTY_FIVE_LAC_DESC, 
                IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC.asDescription());
        assertEquals(IncomeSlab.TWENTY_FIVE_LAC_TO_ONE_CRORE_DESC, 
                IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE.asDescription());
        assertEquals(IncomeSlab.ABOVE_ONE_CRORE_DESC, 
                IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.BELOW_1_LAC),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.BELOW_1_LAC_CODE));
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.ONE_TO_FIVE_LAC_CODE));
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.FIVE_TO_TEN_LAC),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.FIVE_TO_TEN_LAC_CODE));
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.TEN_TO_TWENTY_FIVE_LAC_CODE));
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.ABOVE_TWENTY_FIVE_LAC_CODE));
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE));
        assertEquals(
                Optional.of(IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE),
                IncomeSlab.IncomeSlabType.fromCode(IncomeSlab.ABOVE_ONE_CRORE_CODE));
        assertEquals(Optional.empty(), 
                IncomeSlab.IncomeSlabType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                IncomeSlab.IncomeSlabType.fromCode(null));

        // Test fromString
        assertEquals(IncomeSlab.IncomeSlabType.BELOW_1_LAC, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.BELOW_1_LAC));
        assertEquals(IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.ONE_TO_FIVE_LAC));
        assertEquals(IncomeSlab.IncomeSlabType.FIVE_TO_TEN_LAC, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.FIVE_TO_TEN_LAC));
        assertEquals(IncomeSlab.IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.TEN_TO_TWENTY_FIVE_LAC));
        assertEquals(IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.ABOVE_TWENTY_FIVE_LAC));
        assertEquals(IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.TWENTY_FIVE_LAC_TO_ONE_CRORE));
        assertEquals(IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE, 
                IncomeSlab.IncomeSlabType.fromString(IncomeSlab.ABOVE_ONE_CRORE));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> IncomeSlab.IncomeSlabType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> IncomeSlab.IncomeSlabType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("<1L", IncomeSlab.BELOW_1_LAC);
        assertEquals("01", IncomeSlab.BELOW_1_LAC_CODE);
        assertEquals("Below 1 Lac", IncomeSlab.BELOW_1_LAC_DESC);
        assertEquals("1L-5L", IncomeSlab.ONE_TO_FIVE_LAC);
        assertEquals("02", IncomeSlab.ONE_TO_FIVE_LAC_CODE);
        assertEquals("1 - 5 Lac", IncomeSlab.ONE_TO_FIVE_LAC_DESC);
        assertEquals(">25L", IncomeSlab.ABOVE_TWENTY_FIVE_LAC);
        assertEquals("05", IncomeSlab.ABOVE_TWENTY_FIVE_LAC_CODE);
        assertEquals("Above 25 Lac", IncomeSlab.ABOVE_TWENTY_FIVE_LAC_DESC);
        assertEquals(">1CR", IncomeSlab.ABOVE_ONE_CRORE);
        assertEquals("07", IncomeSlab.ABOVE_ONE_CRORE_CODE);
        assertEquals("Above 1 Crore", IncomeSlab.ABOVE_ONE_CRORE_DESC);
    }

    @Test
    void testIncomeSlabCodes() {
        // Test that the lists contain the expected values
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.BELOW_1_LAC_CODE));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.ONE_TO_FIVE_LAC_CODE));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.FIVE_TO_TEN_LAC_CODE));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.TEN_TO_TWENTY_FIVE_LAC_CODE));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.ABOVE_TWENTY_FIVE_LAC_CODE));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.TWENTY_FIVE_LAC_TO_ONE_CRORE_CODE));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES.contains(IncomeSlab.ABOVE_ONE_CRORE_CODE));
        assertEquals(7, IncomeSlab.INCOME_SLAB_CODES.size());

        assertTrue(IncomeSlab.INCOME_SLAB_CODES_AS_ENUM.contains(
                IncomeSlab.IncomeSlabType.BELOW_1_LAC));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES_AS_ENUM.contains(
                IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES_AS_ENUM.contains(
                IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC));
        assertTrue(IncomeSlab.INCOME_SLAB_CODES_AS_ENUM.contains(
                IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE));
        assertEquals(7, IncomeSlab.INCOME_SLAB_CODES_AS_ENUM.size());
    }

    @Test
    void testIndividualAvailability() {
        // Test individual availability
        assertTrue(IncomeSlab.IncomeSlabType.BELOW_1_LAC.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.FIVE_TO_TEN_LAC.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC.isAvailableForIndividuals());
        
        // These should NOT be available for individuals
        assertFalse(IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE.isAvailableForIndividuals());
        assertFalse(IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE.isAvailableForIndividuals());
    }

    @Test
    void testNonIndividualAvailability() {
        // Test non-individual availability
        assertTrue(IncomeSlab.IncomeSlabType.BELOW_1_LAC.isAvailableForNonIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.ONE_TO_FIVE_LAC.isAvailableForNonIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.FIVE_TO_TEN_LAC.isAvailableForNonIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.TEN_TO_TWENTY_FIVE_LAC.isAvailableForNonIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE.isAvailableForNonIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE.isAvailableForNonIndividuals());
        
        // This should NOT be available for non-individuals
        assertFalse(IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC.isAvailableForNonIndividuals());
    }

    @Test
    void testBusinessLogic() {
        // Test the specific business logic for income slabs
        
        // ABOVE_TWENTY_FIVE_LAC is only for individuals
        assertTrue(IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC.isAvailableForIndividuals());
        assertFalse(IncomeSlab.IncomeSlabType.ABOVE_TWENTY_FIVE_LAC.isAvailableForNonIndividuals());
        
        // TWENTY_FIVE_LAC_TO_ONE_CRORE is only for non-individuals
        assertFalse(IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.TWENTY_FIVE_LAC_TO_ONE_CRORE.isAvailableForNonIndividuals());
        
        // ABOVE_ONE_CRORE is only for non-individuals
        assertFalse(IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.ABOVE_ONE_CRORE.isAvailableForNonIndividuals());
        
        // Lower slabs are available for both
        assertTrue(IncomeSlab.IncomeSlabType.BELOW_1_LAC.isAvailableForIndividuals());
        assertTrue(IncomeSlab.IncomeSlabType.BELOW_1_LAC.isAvailableForNonIndividuals());
    }
}
