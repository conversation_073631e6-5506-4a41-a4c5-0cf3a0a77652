package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for KycMode
 */
class KycModeTest {

    @Test
    void testKycModeConversions() {
        // Test asCode
        assertEquals(KycMode.NORMAL_KYC_CODE, 
                KycMode.KycModeType.NORMAL_KYC.asCode());
        assertEquals(KycMode.E_KYC_OTP_CODE, 
                KycMode.KycModeType.E_KYC_OTP.asCode());
        assertEquals(KycMode.E_KYC_BIOMETRIC_CODE, 
                KycMode.KycModeType.E_KYC_BIOMETRIC.asCode());
        assertEquals(KycMode.ONLINE_DATA_ENTRY_AND_IPV_CODE, 
                KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV.asCode());
        assertEquals(KycMode.OFFLINE_KYC_AADHAAR_CODE, 
                KycMode.KycModeType.OFFLINE_KYC_AADHAAR.asCode());
        assertEquals(KycMode.DIGILOCKER_CODE, 
                KycMode.KycModeType.DIGILOCKER.asCode());
        assertEquals(KycMode.SARAL_CODE, 
                KycMode.KycModeType.SARAL.asCode());

        // Test asDescription
        assertEquals(KycMode.NORMAL_KYC_DESC, 
                KycMode.KycModeType.NORMAL_KYC.asDescription());
        assertEquals(KycMode.E_KYC_OTP_DESC, 
                KycMode.KycModeType.E_KYC_OTP.asDescription());
        assertEquals(KycMode.E_KYC_BIOMETRIC_DESC, 
                KycMode.KycModeType.E_KYC_BIOMETRIC.asDescription());
        assertEquals(KycMode.ONLINE_DATA_ENTRY_AND_IPV_DESC, 
                KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV.asDescription());
        assertEquals(KycMode.OFFLINE_KYC_AADHAAR_DESC, 
                KycMode.KycModeType.OFFLINE_KYC_AADHAAR.asDescription());
        assertEquals(KycMode.DIGILOCKER_DESC, 
                KycMode.KycModeType.DIGILOCKER.asDescription());
        assertEquals(KycMode.SARAL_DESC, 
                KycMode.KycModeType.SARAL.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(KycMode.KycModeType.NORMAL_KYC),
                KycMode.KycModeType.fromCode(KycMode.NORMAL_KYC_CODE));
        assertEquals(
                Optional.of(KycMode.KycModeType.E_KYC_OTP),
                KycMode.KycModeType.fromCode(KycMode.E_KYC_OTP_CODE));
        assertEquals(
                Optional.of(KycMode.KycModeType.E_KYC_BIOMETRIC),
                KycMode.KycModeType.fromCode(KycMode.E_KYC_BIOMETRIC_CODE));
        assertEquals(
                Optional.of(KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV),
                KycMode.KycModeType.fromCode(KycMode.ONLINE_DATA_ENTRY_AND_IPV_CODE));
        assertEquals(
                Optional.of(KycMode.KycModeType.OFFLINE_KYC_AADHAAR),
                KycMode.KycModeType.fromCode(KycMode.OFFLINE_KYC_AADHAAR_CODE));
        assertEquals(
                Optional.of(KycMode.KycModeType.DIGILOCKER),
                KycMode.KycModeType.fromCode(KycMode.DIGILOCKER_CODE));
        assertEquals(
                Optional.of(KycMode.KycModeType.SARAL),
                KycMode.KycModeType.fromCode(KycMode.SARAL_CODE));
        assertEquals(Optional.empty(), 
                KycMode.KycModeType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                KycMode.KycModeType.fromCode(null));

        // Test fromString
        assertEquals(KycMode.KycModeType.NORMAL_KYC, 
                KycMode.KycModeType.fromString(KycMode.NORMAL_KYC));
        assertEquals(KycMode.KycModeType.E_KYC_OTP, 
                KycMode.KycModeType.fromString(KycMode.E_KYC_OTP));
        assertEquals(KycMode.KycModeType.E_KYC_BIOMETRIC, 
                KycMode.KycModeType.fromString(KycMode.E_KYC_BIOMETRIC));
        assertEquals(KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV, 
                KycMode.KycModeType.fromString(KycMode.ONLINE_DATA_ENTRY_AND_IPV));
        assertEquals(KycMode.KycModeType.OFFLINE_KYC_AADHAAR, 
                KycMode.KycModeType.fromString(KycMode.OFFLINE_KYC_AADHAAR));
        assertEquals(KycMode.KycModeType.DIGILOCKER, 
                KycMode.KycModeType.fromString(KycMode.DIGILOCKER));
        assertEquals(KycMode.KycModeType.SARAL, 
                KycMode.KycModeType.fromString(KycMode.SARAL));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> KycMode.KycModeType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> KycMode.KycModeType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("NORMAL_KYC", KycMode.NORMAL_KYC);
        assertEquals("0", KycMode.NORMAL_KYC_CODE);
        assertEquals("Normal KYC", KycMode.NORMAL_KYC_DESC);
        assertEquals("E_KYC_OTP", KycMode.E_KYC_OTP);
        assertEquals("1", KycMode.E_KYC_OTP_CODE);
        assertEquals("e-KYC with OTP", KycMode.E_KYC_OTP_DESC);
        assertEquals("E_KYC_BIOMETRIC", KycMode.E_KYC_BIOMETRIC);
        assertEquals("2", KycMode.E_KYC_BIOMETRIC_CODE);
        assertEquals("e-KYC with Biometric", KycMode.E_KYC_BIOMETRIC_DESC);
        assertEquals("SARAL", KycMode.SARAL);
        assertEquals("6", KycMode.SARAL_CODE);
        assertEquals("SARAL", KycMode.SARAL_DESC);
    }

    @Test
    void testKycModeCodes() {
        // Test that the lists contain the expected values
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.NORMAL_KYC_CODE));
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.E_KYC_OTP_CODE));
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.E_KYC_BIOMETRIC_CODE));
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.ONLINE_DATA_ENTRY_AND_IPV_CODE));
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.OFFLINE_KYC_AADHAAR_CODE));
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.DIGILOCKER_CODE));
        assertTrue(KycMode.KYC_MODE_CODES.contains(KycMode.SARAL_CODE));
        assertEquals(7, KycMode.KYC_MODE_CODES.size());

        assertTrue(KycMode.KYC_MODE_CODES_AS_ENUM.contains(
                KycMode.KycModeType.NORMAL_KYC));
        assertTrue(KycMode.KYC_MODE_CODES_AS_ENUM.contains(
                KycMode.KycModeType.E_KYC_OTP));
        assertTrue(KycMode.KYC_MODE_CODES_AS_ENUM.contains(
                KycMode.KycModeType.E_KYC_BIOMETRIC));
        assertTrue(KycMode.KYC_MODE_CODES_AS_ENUM.contains(
                KycMode.KycModeType.SARAL));
        assertEquals(7, KycMode.KYC_MODE_CODES_AS_ENUM.size());
    }

    @Test
    void testUidAndNewIndividualRequirements() {
        // Test modes that require UID and are for new individuals only
        assertTrue(KycMode.KycModeType.E_KYC_OTP.requiresUidAndNewIndividual());
        assertTrue(KycMode.KycModeType.E_KYC_BIOMETRIC.requiresUidAndNewIndividual());
        assertTrue(KycMode.KycModeType.OFFLINE_KYC_AADHAAR.requiresUidAndNewIndividual());
        assertTrue(KycMode.KycModeType.DIGILOCKER.requiresUidAndNewIndividual());
        
        // Test modes that do NOT require UID and new individual
        assertFalse(KycMode.KycModeType.NORMAL_KYC.requiresUidAndNewIndividual());
        assertFalse(KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV.requiresUidAndNewIndividual());
        assertFalse(KycMode.KycModeType.SARAL.requiresUidAndNewIndividual());
    }

    @Test
    void testBusinessLogic() {
        // Test the specific business logic for KYC modes
        
        // E-KYC modes require UID and are for new individuals only
        assertTrue(KycMode.KycModeType.E_KYC_OTP.requiresUidAndNewIndividual());
        assertTrue(KycMode.KycModeType.E_KYC_BIOMETRIC.requiresUidAndNewIndividual());
        
        // Offline KYC Aadhaar requires UID and is for new individuals only
        assertTrue(KycMode.KycModeType.OFFLINE_KYC_AADHAAR.requiresUidAndNewIndividual());
        
        // Digilocker requires UID and is for new individuals only
        assertTrue(KycMode.KycModeType.DIGILOCKER.requiresUidAndNewIndividual());
        
        // Normal KYC and Online Data Entry do not have these restrictions
        assertFalse(KycMode.KycModeType.NORMAL_KYC.requiresUidAndNewIndividual());
        assertFalse(KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV.requiresUidAndNewIndividual());
        
        // SARAL does not have these restrictions
        assertFalse(KycMode.KycModeType.SARAL.requiresUidAndNewIndividual());
    }

    @Test
    void testSpecificKycModes() {
        // Test specific KYC mode descriptions
        assertEquals("Normal KYC", 
                KycMode.KycModeType.NORMAL_KYC.asDescription());
        assertEquals("e-KYC with OTP", 
                KycMode.KycModeType.E_KYC_OTP.asDescription());
        assertEquals("e-KYC with Biometric", 
                KycMode.KycModeType.E_KYC_BIOMETRIC.asDescription());
        assertEquals("Online Data Entry and IPV", 
                KycMode.KycModeType.ONLINE_DATA_ENTRY_AND_IPV.asDescription());
        assertEquals("Offline KYC - Aadhaar", 
                KycMode.KycModeType.OFFLINE_KYC_AADHAAR.asDescription());
        assertEquals("Digilocker", 
                KycMode.KycModeType.DIGILOCKER.asDescription());
        assertEquals("SARAL", 
                KycMode.KycModeType.SARAL.asDescription());
    }
}
