package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for NriResidentialStatusProof
 */
class NriResidentialStatusProofTest {

    @Test
    void testNriResidentialStatusProofConversions() {
        // Test asCode
        assertEquals(NriResidentialStatusProof.PASSPORT_CODE, 
                NriResidentialStatusProof.NriResidentialStatusProofType.PASSPORT.asCode());
        assertEquals(NriResidentialStatusProof.PIO_CARD_CODE, 
                NriResidentialStatusProof.NriResidentialStatusProofType.PIO_CARD.asCode());
        assertEquals(NriResidentialStatusProof.OCI_CARD_CODE, 
                NriResidentialStatusProof.NriResidentialStatusProofType.OCI_CARD.asCode());

        // Test asDescription
        assertEquals(NriResidentialStatusProof.PASSPORT_DESC, 
                NriResidentialStatusProof.NriResidentialStatusProofType.PASSPORT.asDescription());
        assertEquals(NriResidentialStatusProof.PIO_CARD_DESC, 
                NriResidentialStatusProof.NriResidentialStatusProofType.PIO_CARD.asDescription());
        assertEquals(NriResidentialStatusProof.OCI_CARD_DESC, 
                NriResidentialStatusProof.NriResidentialStatusProofType.OCI_CARD.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(NriResidentialStatusProof.NriResidentialStatusProofType.PASSPORT),
                NriResidentialStatusProof.NriResidentialStatusProofType.fromCode(NriResidentialStatusProof.PASSPORT_CODE));
        assertEquals(
                Optional.of(NriResidentialStatusProof.NriResidentialStatusProofType.PIO_CARD),
                NriResidentialStatusProof.NriResidentialStatusProofType.fromCode(NriResidentialStatusProof.PIO_CARD_CODE));
        assertEquals(
                Optional.of(NriResidentialStatusProof.NriResidentialStatusProofType.OCI_CARD),
                NriResidentialStatusProof.NriResidentialStatusProofType.fromCode(NriResidentialStatusProof.OCI_CARD_CODE));
        assertEquals(Optional.empty(), 
                NriResidentialStatusProof.NriResidentialStatusProofType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                NriResidentialStatusProof.NriResidentialStatusProofType.fromCode(null));

        // Test fromString
        assertEquals(NriResidentialStatusProof.NriResidentialStatusProofType.PASSPORT, 
                NriResidentialStatusProof.NriResidentialStatusProofType.fromString(NriResidentialStatusProof.PASSPORT));
        assertEquals(NriResidentialStatusProof.NriResidentialStatusProofType.PIO_CARD, 
                NriResidentialStatusProof.NriResidentialStatusProofType.fromString(NriResidentialStatusProof.PIO_CARD));
        assertEquals(NriResidentialStatusProof.NriResidentialStatusProofType.OCI_CARD, 
                NriResidentialStatusProof.NriResidentialStatusProofType.fromString(NriResidentialStatusProof.OCI_CARD));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> NriResidentialStatusProof.NriResidentialStatusProofType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> NriResidentialStatusProof.NriResidentialStatusProofType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("PASSPORT", NriResidentialStatusProof.PASSPORT);
        assertEquals("01", NriResidentialStatusProof.PASSPORT_CODE);
        assertEquals("Passport", NriResidentialStatusProof.PASSPORT_DESC);
        assertEquals("PIO_CARD", NriResidentialStatusProof.PIO_CARD);
        assertEquals("02", NriResidentialStatusProof.PIO_CARD_CODE);
        assertEquals("PIO Card", NriResidentialStatusProof.PIO_CARD_DESC);
        assertEquals("OCI_CARD", NriResidentialStatusProof.OCI_CARD);
        assertEquals("03", NriResidentialStatusProof.OCI_CARD_CODE);
        assertEquals("OCI Card", NriResidentialStatusProof.OCI_CARD_DESC);
    }

    @Test
    void testNriResidentialStatusProofCodes() {
        // Test that the lists contain the expected values
        assertTrue(NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES.contains(NriResidentialStatusProof.PASSPORT_CODE));
        assertTrue(NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES.contains(NriResidentialStatusProof.PIO_CARD_CODE));
        assertTrue(NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES.contains(NriResidentialStatusProof.OCI_CARD_CODE));
        assertEquals(3, NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES.size());

        assertTrue(NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES_AS_ENUM.contains(
                NriResidentialStatusProof.NriResidentialStatusProofType.PASSPORT));
        assertTrue(NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES_AS_ENUM.contains(
                NriResidentialStatusProof.NriResidentialStatusProofType.PIO_CARD));
        assertTrue(NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES_AS_ENUM.contains(
                NriResidentialStatusProof.NriResidentialStatusProofType.OCI_CARD));
        assertEquals(3, NriResidentialStatusProof.NRI_RESIDENTIAL_STATUS_PROOF_CODES_AS_ENUM.size());
    }
}
