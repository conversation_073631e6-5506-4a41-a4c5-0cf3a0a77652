package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for Occupation
 */
class OccupationTest {

    @Test
    void testOccupationConversions() {
        // Test asCode
        assertEquals(Occupation.PRIVATE_SECTOR_SERVICE_CODE, 
                Occupation.OccupationType.PRIVATE_SECTOR_SERVICE.asCode());
        assertEquals(Occupation.PUBLIC_SECTOR_SERVICE_CODE, 
                Occupation.OccupationType.PUBLIC_SECTOR_SERVICE.asCode());
        assertEquals(Occupation.BUSINESS_CODE, 
                Occupation.OccupationType.BUSINESS.asCode());
        assertEquals(Occupation.PROFESSIONAL_CODE, 
                Occupation.OccupationType.PROFESSIONAL.asCode());
        assertEquals(Occupation.OTHERS_CODE, 
                Occupation.OccupationType.OTHERS.asCode());

        // Test asDescription
        assertEquals(Occupation.PRIVATE_SECTOR_SERVICE_DESC, 
                Occupation.OccupationType.PRIVATE_SECTOR_SERVICE.asDescription());
        assertEquals(Occupation.PUBLIC_SECTOR_SERVICE_DESC, 
                Occupation.OccupationType.PUBLIC_SECTOR_SERVICE.asDescription());
        assertEquals(Occupation.BUSINESS_DESC, 
                Occupation.OccupationType.BUSINESS.asDescription());
        assertEquals(Occupation.PROFESSIONAL_DESC, 
                Occupation.OccupationType.PROFESSIONAL.asDescription());
        assertEquals(Occupation.OTHERS_DESC, 
                Occupation.OccupationType.OTHERS.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(Occupation.OccupationType.PRIVATE_SECTOR_SERVICE),
                Occupation.OccupationType.fromCode(Occupation.PRIVATE_SECTOR_SERVICE_CODE));
        assertEquals(
                Optional.of(Occupation.OccupationType.PUBLIC_SECTOR_SERVICE),
                Occupation.OccupationType.fromCode(Occupation.PUBLIC_SECTOR_SERVICE_CODE));
        assertEquals(
                Optional.of(Occupation.OccupationType.BUSINESS),
                Occupation.OccupationType.fromCode(Occupation.BUSINESS_CODE));
        assertEquals(
                Optional.of(Occupation.OccupationType.PROFESSIONAL),
                Occupation.OccupationType.fromCode(Occupation.PROFESSIONAL_CODE));
        assertEquals(
                Optional.of(Occupation.OccupationType.OTHERS),
                Occupation.OccupationType.fromCode(Occupation.OTHERS_CODE));
        assertEquals(Optional.empty(), 
                Occupation.OccupationType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                Occupation.OccupationType.fromCode(null));

        // Test fromString
        assertEquals(Occupation.OccupationType.PRIVATE_SECTOR_SERVICE, 
                Occupation.OccupationType.fromString(Occupation.PRIVATE_SECTOR_SERVICE));
        assertEquals(Occupation.OccupationType.PUBLIC_SECTOR_SERVICE, 
                Occupation.OccupationType.fromString(Occupation.PUBLIC_SECTOR_SERVICE));
        assertEquals(Occupation.OccupationType.BUSINESS, 
                Occupation.OccupationType.fromString(Occupation.BUSINESS));
        assertEquals(Occupation.OccupationType.PROFESSIONAL, 
                Occupation.OccupationType.fromString(Occupation.PROFESSIONAL));
        assertEquals(Occupation.OccupationType.OTHERS, 
                Occupation.OccupationType.fromString(Occupation.OTHERS));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> Occupation.OccupationType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> Occupation.OccupationType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("PRIVATE_SECTOR_SERVICE", Occupation.PRIVATE_SECTOR_SERVICE);
        assertEquals("01", Occupation.PRIVATE_SECTOR_SERVICE_CODE);
        assertEquals("Private Sector Service", Occupation.PRIVATE_SECTOR_SERVICE_DESC);
        assertEquals("PUBLIC_SECTOR_SERVICE", Occupation.PUBLIC_SECTOR_SERVICE);
        assertEquals("02", Occupation.PUBLIC_SECTOR_SERVICE_CODE);
        assertEquals("Public Sector Service", Occupation.PUBLIC_SECTOR_SERVICE_DESC);
        assertEquals("BUSINESS", Occupation.BUSINESS);
        assertEquals("03", Occupation.BUSINESS_CODE);
        assertEquals("Business", Occupation.BUSINESS_DESC);
        assertEquals("PROFESSIONAL", Occupation.PROFESSIONAL);
        assertEquals("04", Occupation.PROFESSIONAL_CODE);
        assertEquals("Professional", Occupation.PROFESSIONAL_DESC);
        assertEquals("OTHERS", Occupation.OTHERS);
        assertEquals("99", Occupation.OTHERS_CODE);
        assertEquals("Others", Occupation.OTHERS_DESC);
    }

    @Test
    void testOccupationCodes() {
        // Test that the lists contain the expected values
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.PRIVATE_SECTOR_SERVICE_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.PUBLIC_SECTOR_SERVICE_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.BUSINESS_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.PROFESSIONAL_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.AGRICULTURIST_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.RETIRED_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.HOUSEWIFE_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.STUDENT_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.FOREX_DEALER_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.GOVERNMENT_SERVICE_CODE));
        assertTrue(Occupation.OCCUPATION_CODES.contains(Occupation.OTHERS_CODE));
        assertEquals(11, Occupation.OCCUPATION_CODES.size());

        assertTrue(Occupation.OCCUPATION_CODES_AS_ENUM.contains(
                Occupation.OccupationType.PRIVATE_SECTOR_SERVICE));
        assertTrue(Occupation.OCCUPATION_CODES_AS_ENUM.contains(
                Occupation.OccupationType.PUBLIC_SECTOR_SERVICE));
        assertTrue(Occupation.OCCUPATION_CODES_AS_ENUM.contains(
                Occupation.OccupationType.BUSINESS));
        assertTrue(Occupation.OCCUPATION_CODES_AS_ENUM.contains(
                Occupation.OccupationType.PROFESSIONAL));
        assertTrue(Occupation.OCCUPATION_CODES_AS_ENUM.contains(
                Occupation.OccupationType.OTHERS));
        assertEquals(11, Occupation.OCCUPATION_CODES_AS_ENUM.size());
    }
}
