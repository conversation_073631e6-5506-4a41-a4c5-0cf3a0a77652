package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for ResidentialStatus
 */
class ResidentialStatusTest {

    @Test
    void testResidentialStatusConversions() {
        // Test asCode
        assertEquals(ResidentialStatus.RESIDENT_INDIVIDUAL_CODE, 
                ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL.asCode());
        assertEquals(ResidentialStatus.NON_RESIDENT_INDIVIDUAL_CODE, 
                ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL.asCode());
        assertEquals(ResidentialStatus.FOREIGN_NATIONAL_CODE, 
                ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL.asCode());
        assertEquals(ResidentialStatus.QFI_CODE, 
                ResidentialStatus.ResidentialStatusType.QFI.asCode());
        assertEquals(ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE, 
                ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asCode());

        // Test asDescription
        assertEquals(ResidentialStatus.RESIDENT_INDIVIDUAL_DESC, 
                ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL.asDescription());
        assertEquals(ResidentialStatus.NON_RESIDENT_INDIVIDUAL_DESC, 
                ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL.asDescription());
        assertEquals(ResidentialStatus.FOREIGN_NATIONAL_DESC, 
                ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL.asDescription());
        assertEquals(ResidentialStatus.QFI_DESC, 
                ResidentialStatus.ResidentialStatusType.QFI.asDescription());
        assertEquals(ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_DESC, 
                ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asDescription());

        // Test fromCode
        assertEquals(
                Optional.of(ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL),
                ResidentialStatus.ResidentialStatusType.fromCode(ResidentialStatus.RESIDENT_INDIVIDUAL_CODE));
        assertEquals(
                Optional.of(ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL),
                ResidentialStatus.ResidentialStatusType.fromCode(ResidentialStatus.NON_RESIDENT_INDIVIDUAL_CODE));
        assertEquals(
                Optional.of(ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL),
                ResidentialStatus.ResidentialStatusType.fromCode(ResidentialStatus.FOREIGN_NATIONAL_CODE));
        assertEquals(
                Optional.of(ResidentialStatus.ResidentialStatusType.QFI),
                ResidentialStatus.ResidentialStatusType.fromCode(ResidentialStatus.QFI_CODE));
        assertEquals(
                Optional.of(ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III),
                ResidentialStatus.ResidentialStatusType.fromCode(ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE));
        assertEquals(Optional.empty(), 
                ResidentialStatus.ResidentialStatusType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                ResidentialStatus.ResidentialStatusType.fromCode(null));

        // Test fromString
        assertEquals(ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL, 
                ResidentialStatus.ResidentialStatusType.fromString(ResidentialStatus.RESIDENT_INDIVIDUAL));
        assertEquals(ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL, 
                ResidentialStatus.ResidentialStatusType.fromString(ResidentialStatus.NON_RESIDENT_INDIVIDUAL));
        assertEquals(ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL, 
                ResidentialStatus.ResidentialStatusType.fromString(ResidentialStatus.FOREIGN_NATIONAL));
        assertEquals(ResidentialStatus.ResidentialStatusType.QFI, 
                ResidentialStatus.ResidentialStatusType.fromString(ResidentialStatus.QFI));
        assertEquals(ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III, 
                ResidentialStatus.ResidentialStatusType.fromString(ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> ResidentialStatus.ResidentialStatusType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> ResidentialStatus.ResidentialStatusType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test that constants are correctly defined
        assertEquals("RESIDENT_INDIVIDUAL", ResidentialStatus.RESIDENT_INDIVIDUAL);
        assertEquals("R", ResidentialStatus.RESIDENT_INDIVIDUAL_CODE);
        assertEquals("Resident Individual", ResidentialStatus.RESIDENT_INDIVIDUAL_DESC);
        assertEquals("NON_RESIDENT_INDIVIDUAL", ResidentialStatus.NON_RESIDENT_INDIVIDUAL);
        assertEquals("N", ResidentialStatus.NON_RESIDENT_INDIVIDUAL_CODE);
        assertEquals("Non-Resident Individual", ResidentialStatus.NON_RESIDENT_INDIVIDUAL_DESC);
        assertEquals("FOREIGN_NATIONAL", ResidentialStatus.FOREIGN_NATIONAL);
        assertEquals("P", ResidentialStatus.FOREIGN_NATIONAL_CODE);
        assertEquals("Foreign National", ResidentialStatus.FOREIGN_NATIONAL_DESC);
        assertEquals("QFI", ResidentialStatus.QFI);
        assertEquals("Q", ResidentialStatus.QFI_CODE);
        assertEquals("QFI", ResidentialStatus.QFI_DESC);
        assertEquals("ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III", ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III);
        assertEquals("3", ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE);
        assertEquals("Eligible Foreign Investor Category III", ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_DESC);
    }

    @Test
    void testResidentialStatusCodes() {
        // Test that the lists contain the expected values
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES.contains(ResidentialStatus.RESIDENT_INDIVIDUAL_CODE));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES.contains(ResidentialStatus.NON_RESIDENT_INDIVIDUAL_CODE));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES.contains(ResidentialStatus.FOREIGN_NATIONAL_CODE));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES.contains(ResidentialStatus.QFI_CODE));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES.contains(ResidentialStatus.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III_CODE));
        assertEquals(5, ResidentialStatus.RESIDENTIAL_STATUS_CODES.size());

        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES_AS_ENUM.contains(
                ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES_AS_ENUM.contains(
                ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES_AS_ENUM.contains(
                ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES_AS_ENUM.contains(
                ResidentialStatus.ResidentialStatusType.QFI));
        assertTrue(ResidentialStatus.RESIDENTIAL_STATUS_CODES_AS_ENUM.contains(
                ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III));
        assertEquals(5, ResidentialStatus.RESIDENTIAL_STATUS_CODES_AS_ENUM.size());
    }

    @Test
    void testSpecificResidentialStatusTypes() {
        // Test specific residential status types
        assertEquals("Resident Individual", 
                ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL.asDescription());
        assertEquals("Non-Resident Individual", 
                ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL.asDescription());
        assertEquals("Foreign National", 
                ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL.asDescription());
        assertEquals("QFI", 
                ResidentialStatus.ResidentialStatusType.QFI.asDescription());
        assertEquals("Eligible Foreign Investor Category III", 
                ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asDescription());
        
        // Test codes
        assertEquals("R", ResidentialStatus.ResidentialStatusType.RESIDENT_INDIVIDUAL.asCode());
        assertEquals("N", ResidentialStatus.ResidentialStatusType.NON_RESIDENT_INDIVIDUAL.asCode());
        assertEquals("P", ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL.asCode());
        assertEquals("Q", ResidentialStatus.ResidentialStatusType.QFI.asCode());
        assertEquals("3", ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asCode());
    }

    @Test
    void testForeignInvestorTypes() {
        // Test foreign investor related types
        assertEquals("Foreign National", 
                ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL.asDescription());
        assertEquals("QFI", 
                ResidentialStatus.ResidentialStatusType.QFI.asDescription());
        assertEquals("Eligible Foreign Investor Category III", 
                ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asDescription());
        
        assertEquals("P", ResidentialStatus.ResidentialStatusType.FOREIGN_NATIONAL.asCode());
        assertEquals("Q", ResidentialStatus.ResidentialStatusType.QFI.asCode());
        assertEquals("3", ResidentialStatus.ResidentialStatusType.ELIGIBLE_FOREIGN_INVESTOR_CATEGORY_III.asCode());
    }
}
