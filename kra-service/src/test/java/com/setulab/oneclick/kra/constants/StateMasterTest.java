package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for StateMaster
 */
class StateMasterTest {

    @Test
    void testStateMasterConversions() {
        // Test asCode for key states
        assertEquals(StateMaster.JAMMU_AND_KASHMIR_CODE, 
                StateMaster.StateMasterType.JAMMU_AND_KASHMIR.asCode());
        assertEquals(StateMaster.DELHI_CODE, 
                StateMaster.StateMasterType.DELHI.asCode());
        assertEquals(StateMaster.MAHARASHTRA_CODE, 
                StateMaster.StateMasterType.MAHARASHTRA.asCode());
        assertEquals(StateMaster.KARNATAKA_CODE, 
                StateMaster.StateMasterType.KARNATAKA.asCode());
        assertEquals(StateMaster.TAMIL_NADU_CODE, 
                StateMaster.StateMasterType.TAMIL_NADU.asCode());
        assertEquals(StateMaster.WEST_BENGAL_CODE, 
                StateMaster.StateMasterType.WEST_BENGAL.asCode());
        assertEquals(StateMaster.GUJARAT_CODE, 
                StateMaster.StateMasterType.GUJARAT.asCode());
        assertEquals(StateMaster.RAJASTHAN_CODE, 
                StateMaster.StateMasterType.RAJASTHAN.asCode());
        assertEquals(StateMaster.UTTAR_PRADESH_CODE, 
                StateMaster.StateMasterType.UTTAR_PRADESH.asCode());
        assertEquals(StateMaster.TELANGANA_CODE, 
                StateMaster.StateMasterType.TELANGANA.asCode());

        // Test asDescription for key states
        assertEquals(StateMaster.JAMMU_AND_KASHMIR_DESC, 
                StateMaster.StateMasterType.JAMMU_AND_KASHMIR.asDescription());
        assertEquals(StateMaster.DELHI_DESC, 
                StateMaster.StateMasterType.DELHI.asDescription());
        assertEquals(StateMaster.MAHARASHTRA_DESC, 
                StateMaster.StateMasterType.MAHARASHTRA.asDescription());
        assertEquals(StateMaster.KARNATAKA_DESC, 
                StateMaster.StateMasterType.KARNATAKA.asDescription());
        assertEquals(StateMaster.TAMIL_NADU_DESC, 
                StateMaster.StateMasterType.TAMIL_NADU.asDescription());
        assertEquals(StateMaster.WEST_BENGAL_DESC, 
                StateMaster.StateMasterType.WEST_BENGAL.asDescription());
        assertEquals(StateMaster.GUJARAT_DESC, 
                StateMaster.StateMasterType.GUJARAT.asDescription());
        assertEquals(StateMaster.RAJASTHAN_DESC, 
                StateMaster.StateMasterType.RAJASTHAN.asDescription());
        assertEquals(StateMaster.UTTAR_PRADESH_DESC, 
                StateMaster.StateMasterType.UTTAR_PRADESH.asDescription());
        assertEquals(StateMaster.TELANGANA_DESC, 
                StateMaster.StateMasterType.TELANGANA.asDescription());
    }

    @Test
    void testConstants() {
        // Test that key constants are correctly defined
        assertEquals("JAMMU_AND_KASHMIR", StateMaster.JAMMU_AND_KASHMIR);
        assertEquals("001", StateMaster.JAMMU_AND_KASHMIR_CODE);
        assertEquals("Jammu and Kashmir", StateMaster.JAMMU_AND_KASHMIR_DESC);
        
        assertEquals("DELHI", StateMaster.DELHI);
        assertEquals("007", StateMaster.DELHI_CODE);
        assertEquals("Delhi", StateMaster.DELHI_DESC);
        
        assertEquals("MAHARASHTRA", StateMaster.MAHARASHTRA);
        assertEquals("027", StateMaster.MAHARASHTRA_CODE);
        assertEquals("Maharashtra", StateMaster.MAHARASHTRA_DESC);
        
        assertEquals("KARNATAKA", StateMaster.KARNATAKA);
        assertEquals("029", StateMaster.KARNATAKA_CODE);
        assertEquals("Karnataka", StateMaster.KARNATAKA_DESC);
        
        assertEquals("TAMIL_NADU", StateMaster.TAMIL_NADU);
        assertEquals("033", StateMaster.TAMIL_NADU_CODE);
        assertEquals("Tamil Nadu", StateMaster.TAMIL_NADU_DESC);
        
        assertEquals("WEST_BENGAL", StateMaster.WEST_BENGAL);
        assertEquals("019", StateMaster.WEST_BENGAL_CODE);
        assertEquals("West Bengal", StateMaster.WEST_BENGAL_DESC);
        
        assertEquals("GUJARAT", StateMaster.GUJARAT);
        assertEquals("024", StateMaster.GUJARAT_CODE);
        assertEquals("Gujarat", StateMaster.GUJARAT_DESC);
        
        assertEquals("RAJASTHAN", StateMaster.RAJASTHAN);
        assertEquals("008", StateMaster.RAJASTHAN_CODE);
        assertEquals("Rajasthan", StateMaster.RAJASTHAN_DESC);
        
        assertEquals("UTTAR_PRADESH", StateMaster.UTTAR_PRADESH);
        assertEquals("009", StateMaster.UTTAR_PRADESH_CODE);
        assertEquals("Uttar Pradesh", StateMaster.UTTAR_PRADESH_DESC);
        
        assertEquals("TELANGANA", StateMaster.TELANGANA);
        assertEquals("037", StateMaster.TELANGANA_CODE);
        assertEquals("Telangana", StateMaster.TELANGANA_DESC);
    }

    @Test
    void testStateCodes() {
        // Test that the lists contain the expected values
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.JAMMU_AND_KASHMIR_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.DELHI_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.MAHARASHTRA_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.KARNATAKA_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.TAMIL_NADU_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.WEST_BENGAL_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.GUJARAT_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.RAJASTHAN_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.UTTAR_PRADESH_CODE));
        assertTrue(StateMaster.STATE_CODES.contains(StateMaster.TELANGANA_CODE));
        assertEquals(39, StateMaster.STATE_CODES.size());
    }

    @Test
    void testNorthernStates() {
        // Test northern states
        assertEquals("Jammu and Kashmir", 
                StateMaster.StateMasterType.JAMMU_AND_KASHMIR.asDescription());
        assertEquals("Himachal Pradesh", 
                StateMaster.StateMasterType.HIMACHAL_PRADESH.asDescription());
        assertEquals("Punjab", 
                StateMaster.StateMasterType.PUNJAB.asDescription());
        assertEquals("Chandigarh", 
                StateMaster.StateMasterType.CHANDIGARH.asDescription());
        assertEquals("Uttarakhand", 
                StateMaster.StateMasterType.UTTARAKHAND.asDescription());
        assertEquals("Haryana", 
                StateMaster.StateMasterType.HARYANA.asDescription());
        assertEquals("Delhi", 
                StateMaster.StateMasterType.DELHI.asDescription());
        assertEquals("Rajasthan", 
                StateMaster.StateMasterType.RAJASTHAN.asDescription());
        assertEquals("Uttar Pradesh", 
                StateMaster.StateMasterType.UTTAR_PRADESH.asDescription());
        assertEquals("Bihar", 
                StateMaster.StateMasterType.BIHAR.asDescription());
    }

    @Test
    void testSouthernStates() {
        // Test southern states
        assertEquals("Andhra Pradesh", 
                StateMaster.StateMasterType.ANDHRA_PRADESH.asDescription());
        assertEquals("Karnataka", 
                StateMaster.StateMasterType.KARNATAKA.asDescription());
        assertEquals("Goa", 
                StateMaster.StateMasterType.GOA.asDescription());
        assertEquals("Kerala", 
                StateMaster.StateMasterType.KERALA.asDescription());
        assertEquals("Tamil Nadu", 
                StateMaster.StateMasterType.TAMIL_NADU.asDescription());
        assertEquals("Puducherry", 
                StateMaster.StateMasterType.PUDUCHERRY.asDescription());
        assertEquals("Telangana", 
                StateMaster.StateMasterType.TELANGANA.asDescription());
    }

    @Test
    void testNortheasternStates() {
        // Test northeastern states
        assertEquals("Sikkim", 
                StateMaster.StateMasterType.SIKKIM.asDescription());
        assertEquals("Arunachal Pradesh", 
                StateMaster.StateMasterType.ARUNACHAL_PRADESH.asDescription());
        assertEquals("Assam", 
                StateMaster.StateMasterType.ASSAM.asDescription());
        assertEquals("Manipur", 
                StateMaster.StateMasterType.MANIPUR.asDescription());
        assertEquals("Mizoram", 
                StateMaster.StateMasterType.MIZORAM.asDescription());
        assertEquals("Tripura", 
                StateMaster.StateMasterType.TRIPURA.asDescription());
        assertEquals("Meghalaya", 
                StateMaster.StateMasterType.MEGHALAYA.asDescription());
        assertEquals("Nagaland", 
                StateMaster.StateMasterType.NAGALAND.asDescription());
    }

    @Test
    void testUnionTerritories() {
        // Test union territories
        assertEquals("Chandigarh", 
                StateMaster.StateMasterType.CHANDIGARH.asDescription());
        assertEquals("Delhi", 
                StateMaster.StateMasterType.DELHI.asDescription());
        assertEquals("Daman and Diu", 
                StateMaster.StateMasterType.DAMAN_AND_DIU.asDescription());
        assertEquals("Dadra and Nagar Haveli", 
                StateMaster.StateMasterType.DADRA_AND_NAGAR_HAVELI.asDescription());
        assertEquals("Lakshadweep", 
                StateMaster.StateMasterType.LAKSHADWEEP.asDescription());
        assertEquals("Puducherry", 
                StateMaster.StateMasterType.PUDUCHERRY.asDescription());
        assertEquals("Andaman and Nicobar Islands", 
                StateMaster.StateMasterType.ANDAMAN_AND_NICOBAR_ISLANDS.asDescription());
        assertEquals("Ladakh", 
                StateMaster.StateMasterType.LADAKH.asDescription());
    }

    @Test
    void testSpecialEntries() {
        // Test special entries
        assertEquals("APO", 
                StateMaster.StateMasterType.APO.asDescription());
        assertEquals("Other", 
                StateMaster.StateMasterType.OTHER.asDescription());
        
        assertEquals("036", StateMaster.StateMasterType.APO.asCode());
        assertEquals("039", StateMaster.StateMasterType.OTHER.asCode());
    }
}
