package com.setulab.oneclick.kra.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Optional;

/**
 * Tests for TinExemptReason
 */
class TinExemptReasonTest {

    @Test
    void testTinExemptReasonConversions() {
        // Test asCode for individual reasons
        assertEquals(TinExemptReason.STUDENT_CODE, 
                TinExemptReason.TinExemptReasonType.STUDENT.asCode());
        assertEquals(TinExemptReason.HOME_MAKER_CODE, 
                TinExemptReason.TinExemptReasonType.HOME_MAKER.asCode());
        assertEquals(TinExemptReason.RETIRED_CODE, 
                TinExemptReason.TinExemptReasonType.RETIRED.asCode());

        // Test asCode for non-individual reasons
        assertEquals(TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE, 
                TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON.asCode());
        assertEquals(TinExemptReason.FFI_GIIN_APPLICATION_PENDING_CODE, 
                TinExemptReason.TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING.asCode());
        assertEquals(TinExemptReason.OTHERS_CODE, 
                TinExemptReason.TinExemptReasonType.OTHERS.asCode());

        // Test asDescription
        assertEquals(TinExemptReason.STUDENT_DESC, 
                TinExemptReason.TinExemptReasonType.STUDENT.asDescription());
        assertEquals(TinExemptReason.HOME_MAKER_DESC, 
                TinExemptReason.TinExemptReasonType.HOME_MAKER.asDescription());
        assertEquals(TinExemptReason.RETIRED_DESC, 
                TinExemptReason.TinExemptReasonType.RETIRED.asDescription());
        assertEquals(TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_DESC, 
                TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON.asDescription());
        assertEquals(TinExemptReason.OTHERS_DESC, 
                TinExemptReason.TinExemptReasonType.OTHERS.asDescription());

        // Test asType
        assertEquals(TinExemptReason.STUDENT_TYPE, 
                TinExemptReason.TinExemptReasonType.STUDENT.asType());
        assertEquals(TinExemptReason.HOME_MAKER_TYPE, 
                TinExemptReason.TinExemptReasonType.HOME_MAKER.asType());
        assertEquals(TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_TYPE, 
                TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON.asType());
        assertEquals(TinExemptReason.OTHERS_TYPE, 
                TinExemptReason.TinExemptReasonType.OTHERS.asType());

        // Test fromCode
        assertEquals(
                Optional.of(TinExemptReason.TinExemptReasonType.STUDENT),
                TinExemptReason.TinExemptReasonType.fromCode(TinExemptReason.STUDENT_CODE));
        assertEquals(
                Optional.of(TinExemptReason.TinExemptReasonType.HOME_MAKER),
                TinExemptReason.TinExemptReasonType.fromCode(TinExemptReason.HOME_MAKER_CODE));
        assertEquals(
                Optional.of(TinExemptReason.TinExemptReasonType.RETIRED),
                TinExemptReason.TinExemptReasonType.fromCode(TinExemptReason.RETIRED_CODE));
        assertEquals(
                Optional.of(TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON),
                TinExemptReason.TinExemptReasonType.fromCode(TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE));
        assertEquals(
                Optional.of(TinExemptReason.TinExemptReasonType.OTHERS),
                TinExemptReason.TinExemptReasonType.fromCode(TinExemptReason.OTHERS_CODE));
        assertEquals(Optional.empty(), 
                TinExemptReason.TinExemptReasonType.fromCode("INVALID"));
        assertEquals(Optional.empty(), 
                TinExemptReason.TinExemptReasonType.fromCode(null));

        // Test fromString
        assertEquals(TinExemptReason.TinExemptReasonType.STUDENT, 
                TinExemptReason.TinExemptReasonType.fromString(TinExemptReason.STUDENT));
        assertEquals(TinExemptReason.TinExemptReasonType.HOME_MAKER, 
                TinExemptReason.TinExemptReasonType.fromString(TinExemptReason.HOME_MAKER));
        assertEquals(TinExemptReason.TinExemptReasonType.RETIRED, 
                TinExemptReason.TinExemptReasonType.fromString(TinExemptReason.RETIRED));
        assertEquals(TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON, 
                TinExemptReason.TinExemptReasonType.fromString(TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON));
        assertEquals(TinExemptReason.TinExemptReasonType.OTHERS, 
                TinExemptReason.TinExemptReasonType.fromString(TinExemptReason.OTHERS));

        // Test fromString with invalid input
        assertThrows(
                IllegalArgumentException.class,
                () -> TinExemptReason.TinExemptReasonType.fromString("INVALID"));
        assertThrows(
                IllegalArgumentException.class,
                () -> TinExemptReason.TinExemptReasonType.fromString(null));
    }

    @Test
    void testConstants() {
        // Test individual constants
        assertEquals("STUDENT", TinExemptReason.STUDENT);
        assertEquals("01", TinExemptReason.STUDENT_CODE);
        assertEquals("Student", TinExemptReason.STUDENT_DESC);
        assertEquals("Individual", TinExemptReason.STUDENT_TYPE);
        
        assertEquals("HOME_MAKER", TinExemptReason.HOME_MAKER);
        assertEquals("02", TinExemptReason.HOME_MAKER_CODE);
        assertEquals("Home-maker", TinExemptReason.HOME_MAKER_DESC);
        assertEquals("Individual", TinExemptReason.HOME_MAKER_TYPE);

        assertEquals("RETIRED", TinExemptReason.RETIRED);
        assertEquals("04", TinExemptReason.RETIRED_CODE);
        assertEquals("Retired", TinExemptReason.RETIRED_DESC);
        assertEquals("Individual", TinExemptReason.RETIRED_TYPE);

        // Test non-individual constants
        assertEquals("US_ENTITY_NOT_SPECIFIED_US_PERSON", TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON);
        assertEquals("07", TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE);
        assertEquals("Non-Individual", TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_TYPE);

        assertEquals("OTHERS", TinExemptReason.OTHERS);
        assertEquals("14", TinExemptReason.OTHERS_CODE);
        assertEquals("Others", TinExemptReason.OTHERS_DESC);
        assertEquals("Non-Individual", TinExemptReason.OTHERS_TYPE);
    }

    @Test
    void testIndividualVsNonIndividualLogic() {
        // Test individual reasons
        assertTrue(TinExemptReason.TinExemptReasonType.STUDENT.isForIndividuals());
        assertFalse(TinExemptReason.TinExemptReasonType.STUDENT.isForNonIndividuals());
        
        assertTrue(TinExemptReason.TinExemptReasonType.HOME_MAKER.isForIndividuals());
        assertFalse(TinExemptReason.TinExemptReasonType.HOME_MAKER.isForNonIndividuals());
        
        assertTrue(TinExemptReason.TinExemptReasonType.HOUSE_WIFE.isForIndividuals());
        assertFalse(TinExemptReason.TinExemptReasonType.HOUSE_WIFE.isForNonIndividuals());
        
        assertTrue(TinExemptReason.TinExemptReasonType.RETIRED.isForIndividuals());
        assertFalse(TinExemptReason.TinExemptReasonType.RETIRED.isForNonIndividuals());
        
        assertTrue(TinExemptReason.TinExemptReasonType.DIPLOMAT.isForIndividuals());
        assertFalse(TinExemptReason.TinExemptReasonType.DIPLOMAT.isForNonIndividuals());
        
        assertTrue(TinExemptReason.TinExemptReasonType.TIN_NOT_ELIGIBLE.isForIndividuals());
        assertFalse(TinExemptReason.TinExemptReasonType.TIN_NOT_ELIGIBLE.isForNonIndividuals());

        // Test non-individual reasons
        assertFalse(TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.FFI_GIIN_NOT_REQUIRED.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.FFI_GIIN_NOT_REQUIRED.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.NON_PARTICIPATING_FFI.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.NON_PARTICIPATING_FFI.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.FATCA_EXEMPT_NFFE.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.FATCA_EXEMPT_NFFE.isForNonIndividuals());
        
        assertFalse(TinExemptReason.TinExemptReasonType.OTHERS.isForIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.OTHERS.isForNonIndividuals());
    }

    @Test
    void testTinExemptReasonCodes() {
        // Test that the lists contain the expected values
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES.contains(TinExemptReason.STUDENT_CODE));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES.contains(TinExemptReason.HOME_MAKER_CODE));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES.contains(TinExemptReason.RETIRED_CODE));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES.contains(TinExemptReason.US_ENTITY_NOT_SPECIFIED_US_PERSON_CODE));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES.contains(TinExemptReason.FFI_GIIN_APPLICATION_PENDING_CODE));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES.contains(TinExemptReason.OTHERS_CODE));
        assertEquals(14, TinExemptReason.TIN_EXEMPT_REASON_CODES.size());

        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES_AS_ENUM.contains(
                TinExemptReason.TinExemptReasonType.STUDENT));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES_AS_ENUM.contains(
                TinExemptReason.TinExemptReasonType.HOME_MAKER));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES_AS_ENUM.contains(
                TinExemptReason.TinExemptReasonType.RETIRED));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES_AS_ENUM.contains(
                TinExemptReason.TinExemptReasonType.US_ENTITY_NOT_SPECIFIED_US_PERSON));
        assertTrue(TinExemptReason.TIN_EXEMPT_REASON_CODES_AS_ENUM.contains(
                TinExemptReason.TinExemptReasonType.OTHERS));
        assertEquals(14, TinExemptReason.TIN_EXEMPT_REASON_CODES_AS_ENUM.size());
    }

    @Test
    void testFatcaRelatedReasons() {
        // Test FATCA-related reasons
        assertEquals("FFI - GIIN Application Pending", 
                TinExemptReason.TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING.asDescription());
        assertEquals("NFFE - GIIN Application Pending", 
                TinExemptReason.TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING.asDescription());
        assertEquals("FFI - GIIN Not Required", 
                TinExemptReason.TinExemptReasonType.FFI_GIIN_NOT_REQUIRED.asDescription());
        assertEquals("NFFE - GIIN Not Required", 
                TinExemptReason.TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED.asDescription());
        assertEquals("Non-Participating FFI", 
                TinExemptReason.TinExemptReasonType.NON_PARTICIPATING_FFI.asDescription());
        assertEquals("FATCA Exempt NFFE", 
                TinExemptReason.TinExemptReasonType.FATCA_EXEMPT_NFFE.asDescription());
        
        // All FATCA-related reasons should be for non-individuals
        assertTrue(TinExemptReason.TinExemptReasonType.FFI_GIIN_APPLICATION_PENDING.isForNonIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.NFFE_GIIN_APPLICATION_PENDING.isForNonIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.FFI_GIIN_NOT_REQUIRED.isForNonIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.NFFE_GIIN_NOT_REQUIRED.isForNonIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.NON_PARTICIPATING_FFI.isForNonIndividuals());
        assertTrue(TinExemptReason.TinExemptReasonType.FATCA_EXEMPT_NFFE.isForNonIndividuals());
    }
}
